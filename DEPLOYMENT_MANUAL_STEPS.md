# Manual Steps Required After Deployment

## OAuth Parameter Store Setup

**IMPORTANT**: The OAuth credentials parameters have been created manually due to CloudFormation permission issues. The CloudFormation template no longer attempts to create these parameters automatically.

**Status**: ✅ Parameters have been pre-created for both dev and prod environments with placeholder values.

The parameters are now created manually to avoid CloudFormation deployment failures. You only need to update them with actual credentials.

### Option 1: If CloudFormation Successfully Created the Parameter

If the parameter was created successfully by CloudFormation, update it with actual credentials:

#### For Development Environment

```bash
# Update the existing parameter and convert to SecureString
aws ssm put-parameter \
  --name "/apphero/dev/agent-oap-oauth-tokens" \
  --value "Basic <your-base64-encoded-credentials>" \
  --type "SecureString" \
  --region eu-west-1 \
  --overwrite \
  --description "OAuth credentials for authentication - stores Base64 encoded client_id:client_secret for Basic auth"
```

#### For Production Environment

```bash
# Update the existing parameter and convert to SecureString
aws ssm put-parameter \
  --name "/apphero/prod/agent-oap-oauth-tokens" \
  --value "Basic <your-base64-encoded-credentials>" \
  --type "SecureString" \
  --region eu-west-1 \
  --overwrite \
  --description "OAuth credentials for authentication - stores Base64 encoded client_id:client_secret for Basic auth"
```

### Option 2: If CloudFormation Failed to Create the Parameter

If the CloudFormation deployment failed due to the parameter creation, create it manually:

#### For Development Environment

```bash
# Create the parameter manually as SecureString
aws ssm put-parameter \
  --name "/apphero/dev/agent-oap-oauth-tokens" \
  --value "Basic <your-base64-encoded-credentials>" \
  --type "SecureString" \
  --region eu-west-1 \
  --description "OAuth credentials for authentication - stores Base64 encoded client_id:client_secret for Basic auth" \
  --tags "Key=ENVIRONMENT,Value=dev" "Key=TEAM,Value=EIP Development Team" "Key=PROJECT,Value=APPHERO"
```

#### For Production Environment

```bash
# Create the parameter manually as SecureString
aws ssm put-parameter \
  --name "/apphero/prod/agent-oap-oauth-tokens" \
  --value "Basic <your-base64-encoded-credentials>" \
  --type "SecureString" \
  --region eu-west-1 \
  --description "OAuth credentials for authentication - stores Base64 encoded client_id:client_secret for Basic auth" \
  --tags "Key=ENVIRONMENT,Value=prod" "Key=TEAM,Value=EIP Development Team" "Key=PROJECT,Value=APPHERO"
```

### Option 3: Skip Parameter Creation in CloudFormation

If the parameter creation consistently fails, you can comment out the parameter resource in `parameters.yml` and create the parameter manually before deployment:

1. **Comment out the parameter in `parameters.yml`**:

   ```yaml
   Resources:
     # Commented out - create manually
     # AppHeroOAuthCredentials:
     #   Type: AWS::SSM::Parameter
     #   Properties:
     #     Name: !Sub '/apphero/${self:provider.stage}/agent-oap-oauth-tokens'
     #     # ... rest of config
   ```

2. **Create the parameter manually before deployment** using the commands from Option 2 above

3. **Deploy the CloudFormation stack** without the parameter resource

## Why Create as String First?

The parameter is initially created as a regular String type in CloudFormation because:

1. **CloudFormation Limitations**: Creating SecureString parameters via CloudFormation requires additional KMS permissions that might not be available during stack creation
2. **Deployment Reliability**: String parameters are more reliable to create during initial deployment
3. **Security Best Practice**: The placeholder value is not sensitive, so String type is acceptable initially
4. **Post-Deployment Security**: Converting to SecureString after deployment ensures actual credentials are properly encrypted

## Credential Format

The parameter value must be in the format:

```
Basic <base64-encoded-clientId:clientSecret>
```

### Example:

```bash
# If your credentials are:
# Client ID: your_client_id
# Client Secret: your_client_secret

# Create Base64 string:
echo -n "your_client_id:your_client_secret" | base64
# Result: eW91cl9jbGllbnRfaWQ6eW91cl9jbGllbnRfc2VjcmV0

# Parameter Store value:
Basic eW91cl9jbGllbnRfaWQ6eW91cl9jbGllbnRfc2VjcmV0
```

## Why Update After Deployment?

The OAuth credentials parameter is created via CloudFormation with a placeholder value, then updated manually because:

1. **Security**: Sensitive credentials should not be stored in CloudFormation templates
2. **Automation**: Infrastructure is created automatically via CloudFormation
3. **Flexibility**: Allows different credentials per environment without code changes
4. **Best Practice**: Separates infrastructure creation from secrets management

## Verification

After creating the parameter, you can test the OAuth functionality using the API endpoints documented in `OAUTH_API_DOCUMENTATION.md`.

## Environment Variables

The following environment variables are already configured and point to the correct parameter paths:

- **Dev**: `OAUTH_CREDENTIALS_PARAMETER: /apphero/dev/agent-oap-oauth-tokens`
- **Prod**: `OAUTH_CREDENTIALS_PARAMETER: /apphero/prod/agent-oap-oauth-tokens`
