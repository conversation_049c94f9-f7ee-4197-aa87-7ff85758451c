name: apphero-notification-${sls:stage}
domain:
  name: ${file(env.${self:provider.stage}.yml):variables.APPSYNC_APPHERO_NOTIFICATION_DOMAIN_NAME}
  certificateArn: ${file(env.${self:provider.stage}.yml):variables.APPSYNC_APPHERO_NOTIFICATION_CERTIFICATE_ARN}
  useCloudFormation: false #change to true only for domain creation
  retain: true
  route53: false
authentication:
  type: 'AMAZON_COGNITO_USER_POOLS'
  config:
    awsRegion: ${file(env.${self:provider.stage}.yml):variables.REGION}
    defaultAction: ALLOW
    userPoolId: ${file(env.${self:provider.stage}.yml):variables.APPHERO_USERPOOL_ID}
additionalAuthentications:
  - type: 'AWS_IAM'
apiKeys:
  - name: AppheroNotificationApiKey
    description: apphero notification api key
    expiresAfter: 1y
logging:
  roleArn: arn:aws:iam::${aws:accountId}:role/appsync-logs-${sls:stage}
  level: ALL
  excludeVerboseContent: false
xrayEnabled: true
schema: schema.graphql
dataSources:
  NotificationTable:
    type: AMAZON_DYNAMODB
    config:
      tableName: ${file(env.${self:provider.stage}.yml):variables.APPHERO_NOTIFICATION_TABLE}
      serviceRoleArn: !Sub arn:aws:iam::${aws:accountId}:role/appSyncDynamoDBServiceRole-${sls:stage}
  NotificationSaveAPI:
    type: HTTP
    config:
      endpoint: ${file(env.${self:provider.stage}.yml):variables.NOTIFICATION_DATASOURCE_ENDPOINT}
resolvers:
  Query.getInAppNotificationsByEmail:
    functions:
      - getInAppNotificationsByEmailFunction
  Query.getInAppNotificationByEmail:
    functions:
      - getInAppNotificationByEmailFunction
  Mutation.createNotification:
    functions:
      - createNotificationFunction
  Mutation.updateNotificationStatus:
    functions:
      - updateInAppNotificationFunction
pipelineFunctions:
  getInAppNotificationsByEmailFunction:
    dataSource: NotificationTable
    code: resolvers/query.getInAppNotifications.js
  getInAppNotificationByEmailFunction:
    dataSource: NotificationSaveAPI
    code: resolvers/query.getInAppNotificationsByEmail.js
  createNotificationFunction:
    dataSource: NotificationSaveAPI
    code: resolvers/mutation.createNotification.js
  updateInAppNotificationFunction:
    dataSource: NotificationTable
    code: resolvers/mutation.updateInAppNotificationReadStatus.js
