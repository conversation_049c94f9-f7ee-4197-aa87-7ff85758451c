import { Injectable, NotFoundException } from '@nestjs/common';
import { DynamoDBService } from '../common/dynamodb.service';
import { v4 as uuidv4 } from 'uuid';
import { AuthService } from '../common/auth.service';
import { LookUpService } from 'apps/src/lookup/service';
import { LoggerService } from '../common/cloudwatchService';
import { LoggerEnum } from '@gus-eip/loggers';
const loggerEnum = new LoggerEnum();

@Injectable()
export class CobrandingService {
  constructor(
    private dynamoDBService: DynamoDBService,
    private readonly authService: AuthService,
    private readonly lookUpService: LookUpService,
    private readonly loggerService: LoggerService,
  ) {}
  async generateLink(email: string, brand: string, request?): Promise<string> {
    const requestId = request?.body?.requestId || uuidv4();
    try {
      this.log(
        loggerEnum.Event.INITIATED_GENERATE_COBRAND_URL,
        { email, brand },
        { email, brand },
        'Generate cobranding url initated',
        requestId,
        email,
        'Brand',
        brand,
      );
      const brandExists = await this.checkBrandAvailability(brand);
      if (!brandExists) {
        throw new NotFoundException({
          message: `Brand ${brand} not supported in AppHero`,
          messageCode: 404,
        });
      }

      const uuid = uuidv4();
      await this.storeInDynamoDB(email, brand, uuid);

      this.log(
        loggerEnum.Event.COMPLETED_GENERATE_COBRAND_URL,
        { email, brand },
        { email, brand },
        'Generate cobranding url completed',
        requestId,
        email,
        'Brand',
        brand,
        `${process.env.APPHERO_APP_ENDPOINT}?cobrandId=${uuid}`,
      );

      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        { email, brand },
        { email, brand },
        'Operation completed',
        requestId,
        email,
        'Brand',
        brand,
        `${process.env.APPHERO_APP_ENDPOINT}?cobrandId=${uuid}`,
      );

      return `${process.env.APPHERO_APP_ENDPOINT}?cobrandId=${uuid}`;
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_GENERATE_COBRAND_URL,
        { email, brand },
        { email, brand },
        error.message ? error.message : JSON.stringify(error),
        requestId,
        email,
        'Brand',
        brand,
      );
      throw error;
    }
  }

  private async checkBrandAvailability(brand: string): Promise<boolean> {
    const consumerDetails = await this.dynamoDBService.getObject(
      process.env.CONSUMER_CONFIG_TABLE,
      {
        PK: process.env.GUS_MIDDLEWARE_API_KEY,
      },
    );
    const brandAccessString = consumerDetails.Item.brandAccess;
    const brandAccessList = brandAccessString
      .split(',')
      .map((item) => item.replace(/(^'|'$)/g, '').trim());
    return brandAccessList.includes(brand);
  }
  private async storeInDynamoDB(
    email: string,
    brand: string,
    id: string,
  ): Promise<void> {
    const params = {
      Item: {
        PK: id,
        SK: email,
        email,
        brand,
        id,
      },
    };

    await this.dynamoDBService.putObject(
      process.env.APPHERO_STUDENT_ACCESS_TABLE,
      params,
    );
  }
  async getBrandConfigurationAndEmailStatusById(
    id: string,
    request?,
  ): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];
    try {
      this.log(
        loggerEnum.Event.INITIATED_GET_COBRANDING_DETAILS_BY_ID,
        { id },
        { id },
        'Get brand configuration and email status by id initiated',
        requestId,
        '',
        'coBrandId',
        id,
        null,
        usecase,
      );
      const { email, brand } = await this.getStudentAccessParamsById(id);
      const [brandDetails, emailStatus] = await Promise.all([
        this.lookUpService.getLookUp('Brand', brand),
        this.authService.checkEmailExistInCognito(email),
      ]);

      const { Item } = brandDetails;
      const { coBrandConfig } = Item;

      const updatedCoBrandConfig = {
        ...coBrandConfig,
        emailStatus,
        email,
        brand,
      };

      this.log(
        loggerEnum.Event.COMPLETED_GET_COBRANDING_DETAILS_BY_ID,
        { id },
        { id },
        'Get brand configuration and email status by id completed',
        requestId,
        '',
        'coBrandId',
        id,
        updatedCoBrandConfig,
        usecase,
      );

      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        { id },
        { id },
        'Operation completed',
        requestId,
        '',
        'coBrandId',
        id,
        updatedCoBrandConfig,
        usecase,
      );

      return updatedCoBrandConfig;
    } catch (error) {
      console.error(
        'Error fetching brand configuration and email status:',
        error,
      );
      this.error(
        loggerEnum.Event.FAILED_GET_COBRANDING_DETAILS_BY_ID,
        { id },
        { id },
        error.message ? error.message : JSON.stringify(error),
        requestId,
        '',
        'coBrandId',
        id,
        null,
        usecase,
      );
      throw error;
    }
  }
  async getStudentAccessParamsById(id: string): Promise<any> {
    const queryParams = {
      TableName: process.env.APPHERO_STUDENT_ACCESS_TABLE,
      KeyConditionExpression: 'PK = :pk',
      ExpressionAttributeValues: {
        ':pk': id,
      },
    };
    const accessParams = await this.dynamoDBService.queryObjects(queryParams);

    if (accessParams.Count === 0) {
      throw new NotFoundException('Config Not Found');
    }
    return {
      email: accessParams.Items?.[0]['email'],
      brand: accessParams.Items?.[0]['brand'],
    };
  }

  async log(
    apiEvent,
    sourcePayload,
    destinationPayload,
    logMessage,
    requestId?,
    email?,
    entityKey?,
    entityKeyField?,
    response?,
    usecase?,
  ) {
    await this.loggerService.log(
      requestId || uuidv4(),
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      loggerEnum.UseCase[`${usecase}`],
      sourcePayload,
      destinationPayload,
      logMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }

  async error(
    apiEvent,
    sourcePayload,
    destinationPayload,
    errorMessage,
    requestId?,
    email?,
    entityKey?,
    entityKeyField?,
    response?,
    usecase?,
  ) {
    await this.loggerService.error(
      requestId || uuidv4(),
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      loggerEnum.UseCase[`${usecase}`],
      sourcePayload,
      destinationPayload,
      errorMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }
}
