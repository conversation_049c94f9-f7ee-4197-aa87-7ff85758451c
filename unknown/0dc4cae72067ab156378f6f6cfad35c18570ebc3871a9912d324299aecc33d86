import { INotificationContent } from '../INotificationContent';
export class ReviewCenterContent implements INotificationContent {
  inAppMessage: string;
  emailSubject: string;
  emailBody: string;
  constructor() {
    this.inAppMessage =
      'New tasks have been added to your {{institution}} {{programName}} application. Please review and complete them';
    this.emailSubject =
      'Action Required: Complete Application and Secure Your Place';
  }
}
