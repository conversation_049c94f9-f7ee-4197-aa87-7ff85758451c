import { CloudWatchLoggerService } from '@gus-eip/loggers';

export class LoggerService {
  cloudWatchLoggerService: CloudWatchLoggerService;

  constructor() {
    this.cloudWatchLoggerService = new CloudWatchLoggerService(
      process.env.REGION,
      process.env.LOGGER_LOG_GROUP_NAME,
      process.env.TEAMS_WEBHOOK_URL,
      true,
    );
  }

  async log(
    requestId,
    timestamp,
    component,
    source,
    destination,
    event,
    usecase,
    sourcePayload,
    destinationPayload,
    logMessage,
    brand,
    secondaryKey,
    entityKeyField,
    entityKey,
    destinationObjectType?,
    destinationObjectId?,
    sourceObjectType?,
    sourceObjectId?,
    destinationResponse?,
  ) {
    const logStream = secondaryKey
      ? `apphero-backend-service/${secondaryKey}/${requestId}`
      : `apphero-backend-service/${requestId}`;
    await this.cloudWatchLoggerService.log(
      requestId,
      timestamp,
      component,
      source,
      destination,
      event,
      usecase,
      sourcePayload,
      destinationPayload,
      logMessage,
      brand,
      secondaryKey,
      logStream,
      entityKeyField,
      entityKey,
      destinationObjectType,
      destinationObjectId,
      sourceObjectType,
      sourceObjectId,
      destinationResponse,
    );
  }

  async error(
    requestId,
    timestamp,
    component,
    source,
    destination,
    event,
    usecase,
    sourcePayload,
    destinationPayload,
    errorMessage,
    brand,
    secondaryKey,
    entityKeyField,
    entityKey,
    destinationObjectType?,
    destinationObjectId?,
    sourceObjectType?,
    sourceObjectId?,
    destinationResponse?,
  ) {
    const logStream = secondaryKey
      ? `apphero-backend-service/${secondaryKey}/${requestId}`
      : `apphero-backend-service/${requestId}`;
    await this.cloudWatchLoggerService.error(
      requestId,
      timestamp,
      component,
      source,
      destination,
      event,
      usecase,
      sourcePayload,
      destinationPayload,
      errorMessage,
      brand,
      secondaryKey,
      logStream,
      entityKeyField,
      entityKey,
      destinationObjectType,
      destinationObjectId,
      sourceObjectType,
      sourceObjectId,
      destinationResponse,
    );
  }
}
