import { INotificationContent } from '../INotificationContent';
export class AdmissionStatusUpdateContent implements INotificationContent {
  inAppMessage: string;
  emailSubject: string;
  emailBody: string;
  constructor() {
    this.inAppMessage =
      'The Admission Status of your {{institution}} {{programName}} application has been updated to {{appHeroStage}}.';
    this.emailSubject =
      '{{personAccountName}}, Your Application Status Has Changed';
  }
}
