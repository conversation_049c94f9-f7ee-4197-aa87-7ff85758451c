import { Modu<PERSON> } from '@nestjs/common';
import { VisaApplicationService } from './service';
import { DynamoDBService } from '../common/dynamodb.service';
import { LoggerService } from '../common/cloudwatchService';
import { LoggerEnum } from '@gus-eip/loggers';
import { CommonModule } from '../common/module';
import { SalesforceService } from '../common/salesforce.service';
import { VisaApplicationController } from './controller';
import { OpportunityModule } from '../opportunity/module';
import { S3Service } from '../common/s3.service';
@Module({
  imports: [CommonModule, OpportunityModule],
  controllers: [VisaApplicationController],
  providers: [
    VisaApplicationService,
    DynamoDBService,
    LoggerService,
    SalesforceService,
    LoggerEnum,
    S3Service,
  ],
  exports: [VisaApplicationService],
})
export class VisaApplicationModule {}
