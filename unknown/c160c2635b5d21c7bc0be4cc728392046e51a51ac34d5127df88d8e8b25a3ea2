import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { DynamoDBService } from '../common/dynamodb.service';
import { LoggerService } from '../common/cloudwatchService';
import { LoggerEnum } from '@gus-eip/loggers';
import { v4 as uuidv4 } from 'uuid';
import { SalesforceService } from '../common/salesforce.service';
import { OpportunityService } from '../opportunity/service';
import { S3Service } from '../common/s3.service';

const loggerEnum = new LoggerEnum();

@Injectable()
export class VisaApplicationService {
  private errors: { name: string; message: string }[] = [];
  constructor(
    private readonly dynamoDBService: DynamoDBService,
    private readonly loggerService: LoggerService,
    private readonly salesforceService: SalesforceService,
    private readonly opportunityService: OpportunityService,
    private readonly s3Service: S3Service,
  ) {}

  async visaApplicationProcess(visaApplicationDetails, request?): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];

    await this.log(
      'VISA_APPLICATION_CREATION_INITIATED',
      visaApplicationDetails,
      {},
      'Initiated visa application process',
      requestId,
      visaApplicationDetails.opportunityId,
      'OpportunityId',
      null,
      usecase,
    );

    if (
      !visaApplicationDetails.email ||
      !visaApplicationDetails.opportunityId
    ) {
      throw new HttpException(
        'Email and OpportunityId are required',
        HttpStatus.BAD_REQUEST,
      );
    }

    try {
      let existingOpportunity;
      try {
        existingOpportunity = await this.dynamoDBService.getObject(
          process.env.APPHERO_SF_OPPORTUNITY_TABLE,
          {
            PK: visaApplicationDetails.email,
            SK: visaApplicationDetails.opportunityId,
          },
        );
      } catch (error) {
        this.errors.push({
          name: 'Fetching existing opportunity',
          message: JSON.stringify(error),
        });
      }

      const accountId = this.getAccountId(existingOpportunity);

      console.log('accountId ->', accountId);

      if (accountId) {
        visaApplicationDetails.visaUpdatePayload.Account__c = accountId;
      }

      let salesforceVisaResponse;
      if (visaApplicationDetails.visaUpdatePayload) {
        try {
          salesforceVisaResponse = visaApplicationDetails.Id
            ? await this.salesforceService.patchData(
                `gus/visaapplication/${visaApplicationDetails.Id}`,
                visaApplicationDetails.visaUpdatePayload,
              )
            : await this.createVisaApplicationInSalesforce(
                visaApplicationDetails,
              );
        } catch (error) {
          this.errors.push({
            name: 'Updating/Creating Visa Application in Salesforce',
            message: JSON.stringify(error),
          });
        }
      }

      console.log('salesforceVisaResponse ->', salesforceVisaResponse);

      if (salesforceVisaResponse?.success) {
        let updatedVisaRecords;
        try {
          updatedVisaRecords = this.updateVisaRecords(
            existingOpportunity,
            visaApplicationDetails,
          );
        } catch (error) {
          this.errors.push({
            name: 'Updating Visa Records',
            message: JSON.stringify(error),
          });
        }

        try {
          await this.dynamoDBService.updateObject(
            process.env.APPHERO_SF_OPPORTUNITY_TABLE,
            {
              PK: visaApplicationDetails.email,
              SK: visaApplicationDetails.opportunityId,
            },
            {
              Visa_Application__r: {
                done: true,
                records: updatedVisaRecords,
                totalSize: updatedVisaRecords.length,
              },
              updatedAt: new Date().toISOString(),
            },
          );
        } catch (error) {
          this.errors.push({
            name: 'Updating DynamoDB Visa Application Records',
            message: JSON.stringify(error),
          });
        }
      }

      let visaDocuments = [];
      try {
        if (
          visaApplicationDetails.documentDetails &&
          Array.isArray(visaApplicationDetails.documentDetails)
        ) {
          await this.log(
            'INITIATED_VISA_DOCUMENT_PROCESSING',
            visaApplicationDetails,
            visaApplicationDetails.documentDetails,
            'Initiated document processing',
            requestId,
            visaApplicationDetails.email,
            visaApplicationDetails.opportunityId,
            'opportunityId',
            usecase,
          );

          visaDocuments = await this.processDocuments(visaApplicationDetails);

          await this.log(
            'COMPLETED_VISA_DOCUMENT_PROCESSING',
            visaApplicationDetails,
            visaApplicationDetails.documentDetails,
            'Completed document processing',
            requestId,
            visaApplicationDetails.email,
            visaApplicationDetails.opportunityId,
            'opportunityId',
            usecase,
          );
        } else {
          visaDocuments = [];
        }
      } catch (error) {
        this.errors.push({
          name: 'Processing Documents',
          message: JSON.stringify(error),
        });
      }

      // If there are errors, we'll let the catch block handle them
      if (this.errors.length > 0) {
        console.log(
          'Error',
          this.errors.map((error) => error),
        );

        // Instead of logging and throwing here, we'll throw a simple exception
        // that will be caught by the catch block which will handle the logging
        const errorMessage = `Visa Application Process encountered errors - ${this.errors
          .map((error) => error.name + ': ' + error.message)
          .join(', ')}`;
        throw new Error(errorMessage);
      }

      await this.log(
        'VISA_APPLICATION_PROCESS_COMPLETED',
        visaApplicationDetails,
        { visaApplicationId: visaApplicationDetails.Id },
        'Visa application process completed',
        requestId,
        visaApplicationDetails.email,
        visaApplicationDetails.opportunityId,
        'opportunityId',
        usecase,
      );
      await this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        visaApplicationDetails,
        { visaApplicationId: visaApplicationDetails.Id },
        'Visa application process completed',
        requestId,
        visaApplicationDetails.email,
        visaApplicationDetails.opportunityId,
        'opportunityId',
        usecase,
      );

      return {
        success: true,
        message: 'Visa Application Process completed successfully',
        visaApplicationId: visaApplicationDetails.Id,
        visaDocuments,
      };
    } catch (error) {
      this.errors.push({
        name: 'Visa Application Process',
        message: JSON.stringify(error),
      });

      if (this.errors.length > 0) {
        console.log(
          'Error When processing visa application',
          this.errors.map((error) => error),
        );

        await this.loggerService.error(
          requestId,
          new Date().toISOString(),
          loggerEnum.Component.APPHERO_BACKEND,
          loggerEnum.Component.APPHERO_FRONTEND,
          loggerEnum.Component.GUS_EIP_SERVICE,
          'VISA_APPLICATION_PROCESS_FAILED',
          usecase,
          visaApplicationDetails,
          this.errors,
          'Visa application process failed',
          'AppHero',
          visaApplicationDetails.email,
          'opportunityId',
          visaApplicationDetails.opportunityId,
        );

        this.errors = [];
      }

      throw new HttpException(
        {
          message: `Visa Application Process failed - ${JSON.stringify(error)}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private async createVisaApplicationInSalesforce(visaApplicationDetails) {
    visaApplicationDetails.visaUpdatePayload.Opportunity__c =
      visaApplicationDetails.opportunityId;
    visaApplicationDetails.visaUpdatePayload.Visa_Application_Source__c =
      'AppHero';
    const response = await this.salesforceService.postData(
      'gus/visaapplication',
      visaApplicationDetails.visaUpdatePayload,
    );
    visaApplicationDetails.Id = response.id;
    return response;
  }

  private updateVisaRecords(existingOpportunity, visaApplicationDetails) {
    const visaApplicationRecord = {
      attributes: {
        type: 'Visa_Application__c',
        url: `/services/data/v62.0/sobjects/Visa_Application__c/${visaApplicationDetails.Id}`,
      },
      Id: visaApplicationDetails.Id,
      Opportunity__c: visaApplicationDetails.opportunityId,
      CreatedDate: new Date().toISOString(),
      ...visaApplicationDetails.visaUpdatePayload,
    };

    if (!existingOpportunity?.Item?.Visa_Application__r?.records)
      return [visaApplicationRecord];
    return existingOpportunity.Item.Visa_Application__r.records
      .map((record) =>
        record.Id === visaApplicationDetails.Id
          ? { ...record, ...visaApplicationDetails.visaUpdatePayload }
          : record,
      )
      .concat(
        !existingOpportunity.Item.Visa_Application__r.records.some(
          (record) => record.Id === visaApplicationDetails.Id,
        )
          ? [visaApplicationRecord]
          : [],
      );
  }

  private async processDocuments(visaApplicationDetails) {
    try {
      const brandDetails = visaApplicationDetails.BusinessUnitFilter__c
        ? await this.opportunityService.getBrandDetails(
            visaApplicationDetails.BusinessUnitFilter__c,
          )
        : null;
      const bucketName =
        brandDetails?.Item?.StudentDocument?.StorageBucket ||
        process.env.REVIEW_CENTER_BUCKET_NAME;

      console.log('bucketName ->', bucketName, brandDetails);
      console.log('visaApplicationDetails ->', visaApplicationDetails);

      return Promise.all(
        visaApplicationDetails.documentDetails.map(async (document) => {
          const documentId = uuidv4();

          try {
            if (!document.base64)
              throw new HttpException(
                `base64 data missing for document ${document.fileName}`,
                HttpStatus.BAD_REQUEST,
              );
            const objectKey = `${visaApplicationDetails.opportunityId}/${
              document.documentType
            }/${documentId}.${await this.opportunityService.getFileExtension(
              document.fileName,
            )}`;
            const s3 = await this.s3Service.getS3CredentialsByRole(
              process.env.S3_BUCKET_ACCESS_ROLE_ARN,
            );
            await s3
              .upload({
                Bucket: bucketName,
                Key: objectKey,
                Body: Buffer.from(document.base64, 'base64'),
                ContentType: document.contentType,
              })
              .promise();

            const createOpportunityFilePayload = {
              ApplicationId__c: visaApplicationDetails.applicationFormId,
              DocumentType__c: document.documentType,
              Name: document.fileName,
              FilePath__c: document.fileName,
              Opportunity__c: visaApplicationDetails.opportunityId,
              FullUrl__c: objectKey,
              OriginalValue__c: document.fileName,
              DocumentSource__c: 'AppHero',
              S3FileName__c: objectKey,
              BucketName__c: bucketName,
              Related_Visa_Application__c: visaApplicationDetails.Id,
            };

            const response = await this.salesforceService.postData(
              'gus/opportunityfile',
              createOpportunityFilePayload,
            );
            console.log('response ->', response);

            await this.dynamoDBService.putObject(
              process.env.APPHERO_SF_OPPORTUNITYFILE_TABLE,
              {
                Item: {
                  ...createOpportunityFilePayload,
                  documentId,
                  PK: visaApplicationDetails.opportunityId,
                  SK: response.id,
                  Id: response.id,
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                  Opportunity__r: {
                    BusinessUnitFilter__c:
                      visaApplicationDetails.BusinessUnitFilter__c,
                    Brand__c:
                      visaApplicationDetails.Brand__c ||
                      visaApplicationDetails.BusinessUnitFilter__c,
                  },
                },
              },
            );
            return {
              ...createOpportunityFilePayload,
              documentId,
              PK: visaApplicationDetails.opportunityId,
              SK: response.id,
              Id: response.id,
              Opportunity__r: {
                BusinessUnitFilter__c:
                  visaApplicationDetails.BusinessUnitFilter__c,
                Brand__c:
                  visaApplicationDetails.Brand__c ||
                  visaApplicationDetails.BusinessUnitFilter__c,
              },
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            };
          } catch (error) {
            this.errors.push({
              name: 'Creating Opportunity File',
              message: JSON.stringify(error),
            });
          }
        }),
      );
    } catch (error) {
      this.errors.push({
        name: 'Processing Documents',
        message: JSON.stringify(error),
      });
    }
  }

  getAccountId(existingOpportunity) {
    if (existingOpportunity?.Item?.Account?.Id)
      return existingOpportunity.Item.Account.Id;
    else if (existingOpportunity?.Item?.Account?.attributes)
      return existingOpportunity.Item.Account.attributes.url.split('/').pop();
    else return null;
  }

  async log(
    apiEvent,
    sourcePayload,
    destinationPayload,
    logMessage,
    requestId,
    secondaryKey,
    entityKey?,
    entityKeyField?,
    response?,
    usecase?,
  ) {
    await this.loggerService.log(
      requestId || uuidv4(),
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      loggerEnum.UseCase[usecase],
      sourcePayload,
      destinationPayload,
      logMessage,
      'AppHero',
      secondaryKey,
      entityKeyField,
      entityKey,
      '',
      '',
      '',
      '',
      response,
    );
  }
}
