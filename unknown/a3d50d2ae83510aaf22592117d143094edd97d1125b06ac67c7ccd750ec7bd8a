Resources:
  LookupTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: apphero-lookup-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: ${self:custom.isPITREnabled}
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: APPHERO
  ProgrammeTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: apphero-sf-programme-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: ${self:custom.isPITREnabled}
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: APPHERO
  Product2Table:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: apphero-sf-product2-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: ${self:custom.isPITREnabled}
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: APPHERO
  PriceBookEntryTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: apphero-sf-pricebookentry-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: ${self:custom.isPITREnabled}
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: APPHERO
  OpportunityTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: apphero-sf-opportunity-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: ${self:custom.isPITREnabled}
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: APPHERO
  TaskTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: apphero-sf-task-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: ${self:custom.isPITREnabled}
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: APPHERO
  ApplicationTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: apphero-sf-application-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: ${self:custom.isPITREnabled}
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: APPHERO
  OpportunityFileTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: apphero-sf-opportunityfile-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: ${self:custom.isPITREnabled}
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: APPHERO
  AccountTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: apphero-sf-account-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: ${self:custom.isPITREnabled}
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: APPHERO
  ApplicationBasketTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: apphero-application-basket-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: ${self:custom.isPITREnabled}
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: APPHERO
  CompareProgrammeBasketTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: apphero-compare-programme-basket-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: ${self:custom.isPITREnabled}
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: APPHERO
  AppHeroUserDetailsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: apphero-user-details-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: ${self:custom.isPITREnabled}
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: APPHERO
  AppheroNotificationTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: apphero-notifications-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: ${self:custom.isPITREnabled}
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: APPHERO
  AppheroStudentAccessTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: apphero-student-access-params-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: ${self:custom.isPITREnabled}
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: APPHERO
  AppHeroEventsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: apphero-events-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: ${self:custom.isPITREnabled}
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: APPHERO
