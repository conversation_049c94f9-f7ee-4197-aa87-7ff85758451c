import {
  <PERSON>,
  Post,
  Body,
  HttpException,
  HttpStatus,
  BadRequestException,
  Req,
} from '@nestjs/common';
import { CmsContentService } from './cms-content.service';
import { Request } from 'express';

@Controller('apphero')
export class CmsContentController {
  constructor(private readonly cmsContentService: CmsContentService) {}

  @Post('/content/filter')
  async filterContent(@Body() facts: any, @Req() request: Request) {
    const result = await this.cmsContentService.cmsContents(facts, request);
    return result;
  }

  @Post('/content/events')
  async filterWebinarEvent(@Body() filters: any, @Req() request: Request) {
    if (filters && filters?.email) {
      try {
        const result = await this.cmsContentService.filterWebinarEvents(
          filters,
          request,
        );
        return result;
      } catch (error) {
        throw new HttpException(
          error.message,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    } else {
      throw new BadRequestException('Missing required fields');
    }
  }
}
