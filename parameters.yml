Resources:
  # Parameter Store parameter for OAuth credentials
  # Note: If this fails to create, it might be because the parameter already exists
  # In that case, you can skip this resource and manually update the existing parameter
  #
  # COMMENTED OUT DUE TO CLOUDFORMATION PERMISSION ISSUES
  # Create this parameter manually using the commands in DEPLOYMENT_MANUAL_STEPS.md
  #
  # AppHeroOAuthCredentials:
  #   Type: AWS::SSM::Parameter
  #   Properties:
  #     Name: !Sub '/apphero/${self:provider.stage}/agent-oap-oauth-tokens'
  #     Type: String
  #     Value: 'PLACEHOLDER_VALUE_UPDATE_AFTER_DEPLOYMENT'
  #     Description: 'OAuth credentials for authentication - stores Base64 encoded client_id:client_secret for Basic auth'
  #     Tags:
  #       ENVIRONMENT: ${self:provider.stage}
  #       TEAM: 'EIP Development Team'
  #       PROJECT: 'APPHERO'
