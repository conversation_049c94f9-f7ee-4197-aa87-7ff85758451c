import { util } from '@aws-appsync/utils';

export function request(ctx) {
  const { email, nextToken } = ctx.arguments;
  const limit = ctx.arguments.limit ? ctx.arguments.limit : 5;
  const queryRequest = {
    operation: 'Query',
    query: {
      expression: 'PK = :pkValue and begins_with(SK, :skPrefix)',
      expressionValues: util.dynamodb.toMapValues({
        ':pkValue': email,
        ':skPrefix': 'INAPP#',
      }),
    },
    scanIndexForward: false,
    limit: limit,
  };
  if (nextToken) {
    queryRequest.nextToken = nextToken;
  }
  if (ctx.arguments.filters) {
    queryRequest.filter = {
      expression: 'readStatus = :readStatus',
      expressionValues: util.dynamodb.toMapValues({
        ':readStatus': ctx.arguments.filters.readStatus,
      }),
    };
  }

  return queryRequest;
}

export function response(ctx) {
  if (ctx.error) {
    console.error('Error:', ctx.error.message);
    return util.error(ctx.error.message, ctx.error.type);
  }
  return { items: ctx.result.items, nextToken: ctx.result.nextToken };
}
