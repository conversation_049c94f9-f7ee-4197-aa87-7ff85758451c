import { util } from '@aws-appsync/utils';

export function request(ctx) {
  return {
    operation: 'BatchPutItem',
    tables: {
      Notifications: ctx.arguments.input.map((notification) => {
        // Set createdAt field to current timestamp in ISO format
        notification.createdAt = util.time.nowISO8601();

        // Construct SK (Sort Key)
        const SK = notification.messageDetails.opportunityId
          ? `${notification.type}_${notification.messageDetails.opportunityId}_${notification.event}_${notification.messageDetails.messageId}`
          : `${notification.type}_${notification.event}_${notification.messageDetails.messageId}`;

        // Prepare DynamoDB put request parameters for each notification
        return {
          PK: notification.email,
          SK: SK,
          ...notification,
        };
      }),
    },
  };
}

export function response(ctx) {
  if (ctx.error) {
    return util.error(ctx.error.message, ctx.error.type);
  }

  return ctx.result.input;
}
