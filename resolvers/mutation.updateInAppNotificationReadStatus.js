import { util } from '@aws-appsync/utils';
export function request(ctx) {
  const { createdAt, readStatus, messageId, email } = ctx.args.input;
  const SK = `INAPP#${createdAt}#${messageId}`;
  return {
    operation: 'UpdateItem',
    key: util.dynamodb.toMapValues({ PK: email, SK: SK }),
    update: {
      expression: 'SET #updatedAt = :updatedAt, #readStatus = :readStatus',
      expressionNames: {
        '#updatedAt': 'updatedAt',
        '#readStatus': 'readStatus',
      },
      expressionValues: {
        ':updatedAt': { S: util.time.nowISO8601() },
        ':readStatus': { BOOL: readStatus },
      },
    },
  };
}
export function response(ctx) {
  if (ctx.error) {
    return util.error(ctx.error.message, ctx.error.type);
  }

  return ctx.arguments.input;
}
