schema {
  query: Query
  mutation: Mutation
  subscription: Subscription
}

enum NotificationTypes {
  INAPP
  EMAIL
}

enum NotificationEvents {
  WELCOME_STUDENT
  ADMISSION_STATUS_UPDATE
  REVIEW_CENTER_COMMENT
  ADMISSION_CONDITION
  ISSUED_LETTERS
  AGENT_UPLOADED_DOCUMENT
  TASK_CLOSURE
  APPLICATION_SUBMITTED
  AGENT_TASK_CANCEL
  OPPORTUNITY_UPDATE
  ADMISSION_LETTER_DELETION
  TASK_REOPEN
  SAVE_VISA
  INCORRECT_LOGIN
  UNCONFIRMED_USER
  USER_NOT_CONFIRMED_LOGIN
  CUSTOM_MESSAGE
  SUPPORT_CASE_CREATED
}

type MessageDetailsObject @aws_cognito_user_pools @aws_iam {
  messageId: String!
  opportunityId: String
  taskId: String
  agentAccountName: String
  taskClosedBy: String
}

type NotificationObject @aws_cognito_user_pools @aws_iam {
  message: String!
  messageDetails: MessageDetailsObject!
  createdAt: String!
  updatedAt: String
  type: NotificationTypes!
  readStatus: Boolean!
  event: NotificationEvents!
  email: String!
}

type PaginatedNotificationObject {
  items: [NotificationObject]
  nextToken: String
}

type LimitedNotificationObject {
  items: [NotificationObject]
  total: String
  unreadCount: String
  readCount: String
}

type InAppNotificationReadStatusUpdateObject {
  readStatus: Boolean!
  messageId: String!
  email: String!
  createdAt: String!
}

type Query {
  getInAppNotificationsByEmail(
    email: String!
    limit: Int
    nextToken: String
    filters: NotificationFilterInput
  ): PaginatedNotificationObject
  getInAppNotificationByEmail(
    email: String!
    limit: Int
    offset: Int
    filters: NotificationFilterInput
  ): LimitedNotificationObject
}

input NotificationFilterInput {
  isClosed: Boolean
  readStatus: Boolean
  event: [NotificationEvents]
}

type Mutation {
  createNotification(input: NotificationInput!): NotificationObject
    @aws_cognito_user_pools
    @aws_iam
  updateNotificationStatus(
    input: InAppNotificationReadStatusUpdateInput!
  ): InAppNotificationReadStatusUpdateObject
}

input NotificationInput {
  messageDetails: MessageDetailsInput!
  event: NotificationEvents!
  email: String!
}

input MessageDetailsInput {
  messageId: String!
  opportunityId: String
  taskId: String
  taskCreatedAt: String
  comment: String
  brand: String
  opportunityFileId: String
  documentType: String
  applicationFormId: String
  documentName: String
  stage: String
  appHeroStage: String
  admissionsCondition: String
  programName: String
  personAccountName: String
  isSubsequentComment: Boolean
  isMultiUpload: Boolean
  opportunityFields: OpportunityFieldsInput
  taskClosedBy: String
  studentStatus: String
  visaFields: VisaFieldsInput
  caseNumber: String
}
input VisaFieldsInput {
  visaId: String
  visaApplicationDate: String
  visaApplicationReferenceNumber: String
  visaApplicationStatus: String
  visaInterviewDate: String
  visaNumber: String
  arrivalDate: String
  visaRequired: Boolean
}
input OpportunityFieldsInput {
  studyMode: String
  phone: String
  overallStartDate: String
  location: String
  intakeDate: String
  ownerName: String
  ownerEmail: String
  bookingLink: String
  deliveryMode: String
}

input InAppNotificationReadStatusUpdateInput {
  readStatus: Boolean!
  messageId: String!
  email: String!
  createdAt: String!
}

type Subscription {
  onNewNotification(email: String): NotificationObject
    @aws_subscribe(mutations: ["createNotification"])
    @aws_cognito_user_pools
    @aws_iam
}
