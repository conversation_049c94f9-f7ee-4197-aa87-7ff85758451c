stage: dev
role_arn: arn:aws:iam::${aws:accountId}:role/apphero-lambda-exec-role-${self:provider.stage}
variables:
  REGION: eu-west-1
  APPHERO_USER_DETAILS_TABLE: apphero-user-details-${self:provider.stage}
  PROFILE_PICTURE_BUCKET: apphero-profile-picture-${self:provider.stage}
  GUS_MIDDLEWARE_API: https://dev-api.guseip.io
  GUS_MIDDLEWARE_API_KEY: xphESfRh2o5J7L87WsKfh2MIBWSdPDev4TNPSGNZ
  APPHERO_LOOKUP_TABLE: apphero-lookup-${self:provider.stage}
  APPHERO_APPLICATION_BASKET_TABLE: apphero-application-basket-${self:provider.stage}
  APPHERO_TASK_TABLE: apphero-sf-task-${self:provider.stage}
  APPHERO_APPLICATION_TABLE: apphero-sf-application-${self:provider.stage}
  APPHERO_COMPARE_PROGRAMME_BASKET_TABLE: apphero-compare-programme-basket-${self:provider.stage}
  APPHERO_PROGRAMME_TABLE: apphero-sf-programme-${self:provider.stage}
  S3_BUCKET_ACCESS_ROLE_ARN: arn:aws:iam::************:role/s3CrossAccountAccessRole-prod
  REVIEW_CENTER_BUCKET_NAME: reviewcenter-stage
  ENVIRONMENT_TAG: DEV
  APPHERO_API_ID: aejvix41c7
  APPHERO_USERPOOL_ID: eu-west-1_W2pHL0wmd
  APPHERO_USERPOOL_CLIENT_ID: 5t0r9uvee31oqnba1j4rr0uuo2
  STAGE: dev
  APPHERO_SF_ACCOUNT_TABLE: apphero-sf-account-${self:provider.stage}
  PERSON_ACCOUNT_RECORDTYPE_ID: 0120O0000007L8XQAU
  APPHERO_SF_OPPORTUNITYFILE_TABLE: apphero-sf-opportunityfile-${self:provider.stage}
  APPHERO_SF_OPPORTUNITY_TABLE: apphero-sf-opportunity-${self:provider.stage}
  APPHERO_NOTIFICATION_TABLE: apphero-notifications-dev
  APPHERO_STUDENT_ACCESS_TABLE: apphero-student-access-params-${self:provider.stage}
  NOTIFICATION_DATASOURCE_ENDPOINT: https://dev-api.apphero.io
  SES_MAILER: <EMAIL>
  APPHERO_APP_ENDPOINT: https://stage.apphero.io
  IAM_USER: nishanth.g
  CONSUMER_CONFIG_TABLE: gus-eip-consumer-${self:provider.stage}
  APPSYNC_APPHERO_NOTIFICATION_DOMAIN_NAME: dev-notification-gql.apphero.io
  APPSYNC_APPHERO_NOTIFICATION_CERTIFICATE_ARN: arn:aws:acm:us-east-1:************:certificate/35116c27-4b9b-4a39-8cf5-34d7cd6b2f36
  APPHERO_RECORDTYPE_ID: 012Tf0000016dxNIAQ
  GUS_SALESFORCE_CMS_CHANNEL_ID: 0apds0000000673AAA
  APPHERO_LOGIN_URL: https://dev.apphero.io/login
  APPHERO_BRAND_LOGOS_BUCKET: apphero-brand-logos-${self:provider.stage}
  APPHERO_BRAND_LOGOS_BUCKET_URL: https://apphero-brand-logos-dev.s3.eu-west-1.amazonaws.com
  APPHERO_SF_CMS_CONTENT_TABLE: apphero-sf-cms-content-dev
  LOGGER_LOG_GROUP_NAME: apphero-logs-dev
  TEAMS_WEBHOOK_URL: https://gus.webhook.office.com/webhookb2/f171ec91-8e4c-402d-9191-3af15323b980@5665ee7a-3634-4975-9c21-2778cda48edd/IncomingWebhook/a4408e23fea141a2b6c97dd8712feab0/7f8dc40b-ecac-4851-a3c0-45c9ca0120b1/V22ybuQsfci6Hr2ZRhmCoIQ9C3nY1mAKQuXLBgIIfXVxY1
  GET_TASK_FROM_DATE: 2024-09-01T00:00:00.000Z
  APPHERO_CHATBOT_REQUEST_QUEUE_URL: DEV-APPHERO-CHATBOT-CASE-REQUEST-QUEUE.fifo
  # Salesforce Direct API Configuration (from gus-middleware-service)
  SALESFORCE_API_VERSION: v60.0
  GUS_CONSUMER_KEY: 3MVG9I5UQ_0k_hTlxg9Ks4vR.y9A_7OVQjFs2WVLeuC4yCkJCjlyzTavFKb5xohv3kJ1soWp9E4PcoisP5F5W
  GUS_CONSUMER_SECRET: ****************************************************************
  GUS_AUTH_URL: https://iapro--prodcopy.sandbox.my.salesforce.com/services/oauth2/token
  GUS_ACCESS_TOKEN_SECRET: salesforce-gus-dev-access-token
  GUS_GRANT_TYPE: password
  GUS_USER_NAME: <EMAIL>
  GUS_PASSWORD: 5@{`bdT!!WfKsF5WeAtzaIddyBVMFP5rs8
  # OAuth Parameter Store Configuration
  OAUTH_CREDENTIALS_PARAMETER: /apphero/dev/agent-oap-oauth-tokens
  OAUTH_TOKEN_ENDPOINT: https://dev-authprovider.apphero.io/oauth2/token?grant_type=client_credentials
