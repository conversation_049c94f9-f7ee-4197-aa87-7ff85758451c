version: 0.2
run-as: root

phases:
  install:
    runtime-versions:
      nodejs: 16
    commands:
      - node --version
      - echo "Setting SHELL environment variable..."
      - export SHELL=/bin/bash
      - npm install -g pnpm@8
      - echo "Running pnpm install..."
      - pnpm install
      - echo "Setting up pnpm..."
      - pnpm setup
      - echo "Configuring pnpm to use a custom global bin directory..."
      - mkdir -p /root/.pnpm-global
      - pnpm config set global-dir /root/.pnpm-global
      - echo 'export PATH=$PATH:/root/.pnpm-global/bin' >> /root/.bashrc
      - source /root/.bashrc
      - echo "Installing Serverless Framework..."
      - pnpm install -g serverless@3.38.0
      - pnpm install -g serverless-appsync-plugin
  build:
    commands:
      - echo "Deploying with Serverless Framework..."
      - sls deploy --stage ${stage} --verbose
cache:
  paths:
    - node_modules