# OAuth API Documentation

This API provides endpoints to generate OAuth access tokens by retrieving Basic auth credentials from AWS Parameter Store.

## Base URL
```
https://your-api-domain.com/apphero/oauth
```

## Authentication
These endpoints are part of the AppHero backend service and follow the same authentication patterns as other AppHero APIs.

## Endpoints

### 1. Generate Access Token

**Endpoint**: `POST /apphero/oauth/token`

**Description**: Generates an OAuth access token for a specific brand by retrieving credentials from Parameter Store.

**Request Body**:
```json
{
  "brand": "ucw"  // Optional, defaults to "default"
}
```

**Response**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "scope": "read write",
  "brand": "ucw",
  "generated_at": "2024-01-15T10:30:00.000Z"
}
```

**Example Usage**:
```bash
# Generate token for default brand
curl -X POST https://your-api-domain.com/apphero/oauth/token \
  -H "Content-Type: application/json" \
  -d '{}'

# Generate token for UCW brand
curl -X POST https://your-api-domain.com/apphero/oauth/token \
  -H "Content-Type: application/json" \
  -d '{"brand": "ucw"}'
```

### 2. Get Available Brands

**Endpoint**: `POST /apphero/oauth/brands`

**Description**: Returns a list of all configured brands that have OAuth credentials in Parameter Store.

**Request Body**: `{}` (empty)

**Response**:
```json
{
  "brands": ["DEFAULT", "UCW"],
  "total": 2
}
```

**Example Usage**:
```bash
curl -X POST https://your-api-domain.com/apphero/oauth/brands \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 3. Validate Brand Credentials

**Endpoint**: `POST /apphero/oauth/validate`

**Description**: Validates that credentials for a specific brand are accessible in Parameter Store.

**Request Body**:
```json
{
  "brand": "ucw"  // Optional, defaults to "default"
}
```

**Response**:
```json
{
  "brand": "ucw",
  "valid": true,
  "parameter_path": "/apphero/dev/oauth/ucw/client-credentials"
}
```

**Example Usage**:
```bash
curl -X POST https://your-api-domain.com/apphero/oauth/validate \
  -H "Content-Type: application/json" \
  -d '{"brand": "ucw"}'
```

## Error Responses

### 400 Bad Request
```json
{
  "statusCode": 400,
  "message": "Brand must be a non-empty string",
  "error": "Bad Request"
}
```

### 500 Internal Server Error
```json
{
  "statusCode": 500,
  "message": "Failed to generate OAuth token",
  "error": "Parameter /apphero/dev/oauth/ucw/client-credentials not found",
  "brand": "ucw"
}
```

## Parameter Store Requirements

For the API to work, you need to manually create the following parameters in AWS Parameter Store:

### Default Brand
```bash
aws ssm put-parameter \
  --name "/apphero/dev/oauth/client-credentials" \
  --value "Basic <your-base64-encoded-credentials>" \
  --type "SecureString" \
  --region eu-west-1
```

### UCW Brand
```bash
aws ssm put-parameter \
  --name "/apphero/dev/oauth/ucw/client-credentials" \
  --value "Basic <your-ucw-base64-encoded-credentials>" \
  --type "SecureString" \
  --region eu-west-1
```

### Production Parameters
```bash
# Default
aws ssm put-parameter \
  --name "/apphero/prod/oauth/client-credentials" \
  --value "Basic <your-prod-base64-encoded-credentials>" \
  --type "SecureString" \
  --region eu-west-1

# UCW
aws ssm put-parameter \
  --name "/apphero/prod/oauth/ucw/client-credentials" \
  --value "Basic <your-ucw-prod-base64-encoded-credentials>" \
  --type "SecureString" \
  --region eu-west-1
```

## Credential Format

The Parameter Store values must be in the format:
```
Basic <base64-encoded-clientId:clientSecret>
```

**Example**:
```bash
# If your credentials are:
# Client ID: your_client_id
# Client Secret: your_client_secret

# Create Base64 string:
echo -n "your_client_id:your_client_secret" | base64
# Result: eW91cl9jbGllbnRfaWQ6eW91cl9jbGllbnRfc2VjcmV0

# Parameter Store value:
Basic eW91cl9jbGllbnRfaWQ6eW91cl9jbGllbnRfc2VjcmV0
```

## Integration Example

```typescript
// Example service integration
export class YourService {
  async getAccessToken(brand: string = 'default') {
    try {
      const response = await axios.post('/apphero/oauth/token', {
        brand: brand
      });
      
      return response.data.access_token;
    } catch (error) {
      console.error('Failed to get access token:', error);
      throw error;
    }
  }
}
```

## Security Notes

1. **Parameter Store**: All credentials are stored as SecureString (encrypted)
2. **Access Control**: IAM policies restrict access to specific parameter paths
3. **Logging**: All token generation attempts are logged
4. **Environment Separation**: Dev and prod parameters are completely separate

## Adding New Brands

To add a new brand (e.g., "HZU"):

1. **Create Parameter Store parameter**:
   ```bash
   aws ssm put-parameter \
     --name "/apphero/dev/oauth/hzu/client-credentials" \
     --value "Basic <hzu-base64-credentials>" \
     --type "SecureString" \
     --region eu-west-1
   ```

2. **The API will automatically discover the new brand** on the next request

3. **Test the new brand**:
   ```bash
   curl -X POST https://your-api-domain.com/apphero/oauth/token \
     -H "Content-Type: application/json" \
     -d '{"brand": "hzu"}'
   ```

The API automatically discovers available brands from Parameter Store, so no code changes are required when adding new brands!
