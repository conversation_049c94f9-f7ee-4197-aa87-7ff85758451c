import {
  Controller,
  Post,
  Body,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { NotificationsService } from './notification.service';
@Controller('unauth')
export class NotificaitonController {
  constructor(private readonly notificationsService: NotificationsService) {}
  @Post('/apphero/createnotification')
  async createNotification(@Body() event): Promise<any> {
    try {
      return await this.notificationsService.createNotification(event.input);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Post('/apphero/getnotificationbyemail')
  async getNotificationByEmail(@Body() event): Promise<any> {
    try {
      return await this.notificationsService.getInAppNotificationByEmail(event);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
