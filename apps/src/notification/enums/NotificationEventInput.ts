import { AppHeroStage } from './AppHeroStage';
import { NotificationEvent } from './NotificationEventType';
import { StageName } from './StageName';
export interface EventDetailsInput {
  messageId: string;
  opportunityId: string;
  taskId?: string;
  taskCreatedAt?: string;
  comment?: string;
  brand?: string;
  opportunityFileId?: string;
  applicationFormId?: string;
  documentType?: string;
  documentName?: string;
  stage?: StageName;
  appHeroStage?: AppHeroStage;
  admissionsCondition?: string;
  programName?: string;
  personAccountName?: string;
  isSubsequentComment?: boolean;
  isMultiUpload?: boolean;
  taskClosedBy?: string;
  taskComment?: string;
  caseNumber?: string;
}
export interface NotificationInput {
  messageDetails: EventDetailsInput;
  email: string;
  event: NotificationEvent;
}
