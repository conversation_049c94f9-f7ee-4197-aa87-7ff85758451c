import { NotificationEvent } from './NotificationEventType';
import { AppHeroStage } from './AppHeroStage';
import { StageName } from './StageName';
export interface NotificationEventDetails {
  messageId: string;
  opportunityId: string;
  taskId?: string;
  taskCreatedAt?: string;
  comment?: string;
  brand?: string;
  opportunityFileId?: string;
  applicationFormId?: string;
  documentType?: string;
  documentName?: string;
  stage?: StageName;
  appHeroStage?: AppHeroStage;
  admissionsCondition?: string;
  programName?: string;
  institution?: string;
  personAccountName?: string;
  agentAccountName?: string;
  agentContactName?: string;
  studentBrand?: string;
  taskClosedBy?: string;
  advisorName?: string;
  opportunityFields?: OpportunityFieldsInput;
  studentStatus?: string;
  visaFields?: VisaFieldsInput;
  visaRecords?: any;
  taskComment?: string;
  caseNumber?: string;
  isMultiUpload?: boolean;
}
export interface OpportunityFieldsInput {
  studyMode?: string;
  phone?: string;
  overallStartDate?: string;
  location?: string;
  intakeDate?: string;
  ownerName?: string;
  ownerEmail?: string;
  bookingLink?: string;
  deliveryMode?: string;
}
export interface VisaFieldsInput {
  visaId?: string;
  visaApplicationDate?: string;
  visaApplicationReferenceNumber?: string;
  visaApplicationStatus?: string;
  visaInterviewDate?: string;
  visaNumber?: string;
  arrivalDate?: string;
  visaRequired?: boolean;
}
export interface NotificationEventDetail {
  messageDetails: NotificationEventDetails;
  email: string;
  event: NotificationEvent;
  message?: string;
}
