import { AppHeroStage } from './AppHeroStage';
import { NotificationEvent } from './NotificationEventType';

export interface EventAdditionalDetail {
  programName?: string;
  institution?: string;
  personAccountName?: string;
  agentAccountName?: string;
  agentContactName?: string;
  personEmail?: string;
  buttonUrl?: string;
  currentStage?: AppHeroStage;
  studentBrand?: string;
  event?: NotificationEvent;
  advisorName?: string;
  visaRecords?: any;
}
