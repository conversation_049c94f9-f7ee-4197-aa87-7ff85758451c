export enum NotificationEvent {
  WEL<PERSON><PERSON>_STUDENT,
  ADMISSION_STATUS_UPDATE,
  REVIEW_CENTER_COMMENT,
  ADMISSION_CONDITION,
  ISSUED_LETTERS,
  AGENT_UPLOADED_DOCUMENT,
  TASK_CLOSURE,
  APPLICATION_SUBMITTED,
  APPLICATION_SUBMITTED_JOIN_APPHERO,
  APPLICATION_SUBMITTED_TRACK_NEW_APPLICATION,
  AGENT_TASK_<PERSON>NCEL,
  OPPORTUNITY_UPDATE,
  ADMISSION_LETTER_DELETION,
  TASK_REOPEN,
  SAVE_VISA,
  INCORRECT_LOGIN,
  UNCONFIRMED_USER,
  USER_NOT_CONFIRMED_LOGIN,
  SUPPORT_CASE_CREATED,
}
