import { DynamoDBService } from 'apps/src/common/dynamodb.service';
import * as AWS from 'aws-sdk';
import { Injectable } from '@nestjs/common';
import { NotificationInput } from './enums/NotificationEventInput';
import { NotificationContentBuilder } from './notificationContentBuilder/notificationContentBuilder';
import { EventAdditionalDetail } from './enums/EventAdditionalDetail';
import { NotificationEventDetail } from './enums/NotificationEventDetail';
import { EmailTemplate } from './enums/EmailTemplate';
import { INotificationService } from './INotificationService';
import { OpportunityService } from '../opportunity/service';
import { LoggerService } from '../common/cloudwatchService';
import { v4 as uuidv4 } from 'uuid';
import { LoggerEnum } from '@gus-eip/loggers';
const loggerEnum = new LoggerEnum();
@Injectable()
export class NotificationsService implements INotificationService {
  constructor(
    private readonly dynamoDBService: DynamoDBService,
    private readonly notificationContentBuilder: NotificationContentBuilder,
    private readonly opportunityService: OpportunityService,
    private readonly loggerService: LoggerService,
  ) {}
  async createNotification(payload: NotificationInput) {
    console.log('payload -->', payload);
    let { email, event } = payload;
    console.log('email -->', email);
    try {
      this.log(
        payload?.messageDetails?.messageId,
        'APPHERO_NOTIFICATION_LISTENER',
        '',
        loggerEnum.Event.INITIATED_NOTIFICATION,
        loggerEnum.UseCase[`NOTIFICATION_${payload.event}`],
        'Create notification initiated',
        payload?.messageDetails?.opportunityId,
        payload?.messageDetails?.opportunityId,
        'opportunityId',
        payload,
        {},
        payload?.messageDetails?.brand,
      );
      let additionalDetails: EventAdditionalDetail;
      let canSendNotification = true;
      if (payload.messageDetails.opportunityId) {
        additionalDetails =
          await this.notificationContentBuilder.getAdditionalInfo(
            payload.messageDetails,
            email,
            event,
          );
        console.log('additionalDetails -->', additionalDetails);
        event = additionalDetails.event;
        canSendNotification =
          await this.notificationContentBuilder.checkCanSendNotification(
            additionalDetails,
            event,
            payload,
          );
        console.log('canSendNotification', canSendNotification);
      } else {
        const opportunityIdResponse =
          await this.opportunityService.getOpportunitiesByEmail({
            email: email,
          });
        console.log(
          'opportunities for the email',
          opportunityIdResponse.response,
        );
        payload.messageDetails.opportunityId =
          opportunityIdResponse?.response[0]?.Id;
        payload.messageDetails.brand =
          opportunityIdResponse?.response[0]?.Brand__c;
        console.log('payload', payload.messageDetails);
        additionalDetails =
          await this.notificationContentBuilder.getAdditionalInfo(
            payload.messageDetails,
            email,
            event,
          );
        console.log('additionalDetails -->', additionalDetails);
        event = additionalDetails.event;
        canSendNotification =
          await this.notificationContentBuilder.checkCanSendNotification(
            additionalDetails,
            event,
            payload,
          );
        console.log('canSendNotification', canSendNotification);
      }

      const usecase = `NOTIFICATION_${event}`;
      console.log('Usecase ->', usecase);
      if (canSendNotification && !payload.messageDetails.isSubsequentComment) {
        if (payload.messageDetails.comment) {
          payload.messageDetails.taskComment = this.parseComment(
            payload.messageDetails.comment,
          );
        }
        const notificationObject: NotificationEventDetail = {
          email,
          event,
          messageDetails: { ...payload.messageDetails, ...additionalDetails },
        };
        this.log(
          payload?.messageDetails?.messageId,
          'APPHERO_NOTIFICATION_LISTENER',
          '',
          loggerEnum.Event.SAVED_NOTIFICATION,
          loggerEnum.UseCase[`${usecase}`],
          'Save notification initiated',
          payload?.messageDetails?.opportunityId,
          payload?.messageDetails?.opportunityId,
          'opportunityId',
          payload,
          {},
          payload?.messageDetails?.brand,
          notificationObject,
        );
        const response = await this.notificationContentBuilder.saveInDb(
          event,
          notificationObject,
        );
        this.log(
          payload?.messageDetails?.messageId,
          'APPHERO_NOTIFICATION_LISTENER',
          '',
          loggerEnum.Event.COMPLETED_NOTIFICATION,
          loggerEnum.UseCase[`${usecase}`],
          'Save notification completed',
          payload?.messageDetails?.opportunityId,
          payload?.messageDetails?.opportunityId,
          'opportunityId',
          payload,
          response,
          payload?.messageDetails?.brand,
          notificationObject,
        );
        const inAppMessage: string =
          await this.notificationContentBuilder.getInAppEventText(
            event,
            notificationObject.messageDetails,
          );
        console.log('inAppMessage -->', inAppMessage);
        this.log(
          payload?.messageDetails?.messageId,
          'APPHERO_NOTIFICATION_LISTENER',
          '',
          loggerEnum.Event.COMPLETED_INAPP_NOTIFICATION_CONTENT,
          loggerEnum.UseCase[`${usecase}`],
          'get inapp notification content completed',
          payload?.messageDetails?.opportunityId,
          payload?.messageDetails?.opportunityId,
          'opportunityId',
          notificationObject,
          inAppMessage,
          payload?.messageDetails?.brand,
        );
        notificationObject.message = inAppMessage;
        const emailTemplate: EmailTemplate =
          await this.notificationContentBuilder.getEmailNotificationContent(
            email,
            event,
            notificationObject.messageDetails,
          );

        console.log('emailTemplate', emailTemplate);
        this.log(
          payload?.messageDetails?.messageId,
          'APPHERO_NOTIFICATION_LISTENER',
          '',
          loggerEnum.Event.COMPLETED_EMAIL_NOTIFICATION_TEMPLATE,
          loggerEnum.UseCase[`${usecase}`],
          'get inapp notification completed',
          payload?.messageDetails?.opportunityId,
          payload?.messageDetails?.opportunityId,
          'opportunityId',
          notificationObject,
          emailTemplate,
          payload?.messageDetails?.brand,
        );
        if (emailTemplate) {
          console.log('sending mail ...');
          await this.sendEmail(
            email,
            emailTemplate.subject,
            emailTemplate.body,
            payload,
          );
          this.log(
            payload?.messageDetails?.messageId,
            'APPHERO_NOTIFICATION_LISTENER',
            '',
            loggerEnum.Event.SENT_EMAIL,
            loggerEnum.UseCase[`${usecase}`],
            'email notification sent',
            payload?.messageDetails?.opportunityId,
            payload?.messageDetails?.opportunityId,
            'opportunityId',
            notificationObject,
            emailTemplate,
            payload?.messageDetails?.brand,
          );
        }
        this.log(
          payload?.messageDetails?.messageId,
          'APPHERO_NOTIFICATION_LISTENER',
          '',
          loggerEnum.Event.OPERATION_COMPLETED,
          loggerEnum.UseCase[`${usecase}`],
          'Operation completed',
          payload?.messageDetails?.opportunityId,
          payload?.messageDetails?.opportunityId,
          'opportunityId',
          payload,
          {},
          payload?.messageDetails?.brand,
        );
        return await this.saveNotification(notificationObject);
      } else {
        this.log(
          payload?.messageDetails?.messageId,
          'APPHERO_NOTIFICATION_LISTENER',
          '',
          loggerEnum.Event.NOT_REQUIRED_NOTIFICATION,
          loggerEnum.UseCase[`${usecase}`],
          'Notification not required',
          payload?.messageDetails?.opportunityId,
          payload?.messageDetails?.opportunityId,
          'opportunityId',
          payload,
          {},
          payload?.messageDetails?.brand,
        );
        this.log(
          payload?.messageDetails?.messageId,
          'APPHERO_NOTIFICATION_LISTENER',
          '',
          loggerEnum.Event.OPERATION_COMPLETED,
          loggerEnum.UseCase[`${usecase}`],
          'Operation completed',
          payload?.messageDetails?.opportunityId,
          payload?.messageDetails?.opportunityId,
          'opportunityId',
          payload,
          {},
          payload?.messageDetails?.brand,
        );
      }
    } catch (error) {
      this.error(
        payload?.messageDetails?.messageId,
        'APPHERO_NOTIFICATION_LISTENER',
        '',
        'FAILED_CREATE_NOTIFICATION',
        loggerEnum.UseCase[`NOTIFICATION_${payload.event}`],
        error.message ? error.message : JSON.stringify(error),
        payload?.messageDetails?.opportunityId,
        payload?.messageDetails?.opportunityId,
        'opportunityId',
        payload,
        {},
        payload?.messageDetails?.brand,
      );
    }
  }

  async getInAppNotificationByEmail(input): Promise<any> {
    const eventId = uuidv4();
    const { email, limit, filters, offset } = input;
    try {
      this.log(
        eventId,
        loggerEnum.Component.APPHERO_FRONTEND,
        '',
        loggerEnum.Event.INITIATED_GET_NOTIFICATION_BY_EMAIL,
        'GET_NOTIFICATION',
        'get in app notifications by email',
        email,
        email,
        'email',
      );
      const queryRequest: AWS.DynamoDB.DocumentClient.QueryInput = {
        TableName: process.env.APPHERO_NOTIFICATION_TABLE,
        KeyConditionExpression: 'PK = :pkValue and begins_with(SK, :skPrefix)',
        ExpressionAttributeValues: {
          ':pkValue': email,
          ':skPrefix': 'INAPP#',
        },
        ScanIndexForward: false,
      };
      this.log(
        eventId,
        loggerEnum.Component.APPHERO_FRONTEND,
        '',
        loggerEnum.Event.INITIATED_GET_NOTIFICATION_FROM_DB,
        'GET_NOTIFICATION',
        'get in app notifications from db by email',
        email,
        email,
        'email',
        queryRequest,
      );

      const result = await this.dynamoDBService.queryObjects(queryRequest);
      this.log(
        eventId,
        loggerEnum.Component.APPHERO_FRONTEND,
        loggerEnum.Component.APPHERO_FRONTEND,
        'COMPLETED_GET_NOTIFICATION_FROM_DB',
        'GET_NOTIFICATION',
        'get in app notifications from db by email',
        email,
        email,
        'email',
        queryRequest,
        result,
      );
      let notifications = await result.Items;
      this.log(
        eventId,
        loggerEnum.Component.APPHERO_FRONTEND,
        '',
        loggerEnum.Event.INITIATED_FILTER_NOTIFICATION,
        'GET_NOTIFICATION',
        'filter notification complated',
        email,
        email,
        'email',
        notifications,
      );
      filters
        ? (notifications = await this.filterNotification(
            filters,
            notifications,
          ))
        : notifications;
      this.log(
        eventId,
        loggerEnum.Component.APPHERO_FRONTEND,
        '',
        loggerEnum.Event.COMPLETED_FILTER_NOTIFICATION,
        'GET_NOTIFICATION',
        'filter notification complated',
        email,
        email,
        'email',
        notifications,
        filters,
      );
      if (notifications.length > 0) {
        const unreadNotifications = notifications.filter(
          (notification) => notification.readStatus === false,
        );
        const readNotifications = notifications.filter(
          (notification) => notification.readStatus === true,
        );
        if (limit && offset) {
          notifications = notifications.slice(offset, offset + limit);
        }
        this.log(
          eventId,
          loggerEnum.Component.APPHERO_FRONTEND,
          '',
          loggerEnum.Event.COMPLETED_GET_NOTIFICATION,
          'GET_NOTIFICATION',
          'get notification complated',
          email,
          email,
          'email',
          input,
          notifications,
        );

        this.log(
          eventId,
          loggerEnum.Component.APPHERO_FRONTEND,
          '',
          loggerEnum.Event.OPERATION_COMPLETED,
          'GET_NOTIFICATION',
          'Operation completed',
          email,
          email,
          'email',
          input,
          notifications,
        );

        return {
          items: notifications,
          total: notifications.length,
          unreadCount: unreadNotifications.length,
          readCount: readNotifications.length,
        };
      } else {
        return {
          items: [],
          total: 0,
          unreadCount: 0,
          readCount: 0,
        };
      }
    } catch (error) {
      this.error(
        eventId,
        loggerEnum.Component.APPHERO_FRONTEND,
        '',
        loggerEnum.Event.FAILED_GET_NOTIFICATION_BY_EMAIL,
        'GET_NOTIFICATION',
        error.message ? error.message : JSON.stringify(error),
        email,
        email,
        'email',
      );
    }
  }
  async filterNotification(filters, notifications): Promise<any> {
    const filterResults = () => {
      return notifications.filter((item) => {
        for (const field in filters) {
          const filterValue = filters[field];
          if (Array.isArray(filterValue)) {
            if (!filterValue.includes(item[field])) {
              return false;
            }
          } else if (filterValue !== undefined && filterValue !== item[field]) {
            return false;
          }
        }
        return true;
      });
    };
    return filterResults();
  }
  async sendEmail(
    email: string,
    subject: string,
    body: string,
    payload: any,
  ): Promise<void> {
    const customHeaders = Object.entries(payload)
      .map(([key, value]) => {
        if (typeof value === 'object') {
          return `${key}: ${JSON.stringify(value)}`;
        }
        return `${key}: ${value}`;
      })
      .join('\r\n');

    const rawMessage = [
      `From: ${process.env.SES_MAILER}`,
      `To: ${email}`,
      `Subject: ${subject}`,
      `MIME-Version: 1.0`,
      `Content-Type: text/html; charset=UTF-8`,
      `X-Source: Apphero`,
      customHeaders,
      '',
      body,
    ].join('\r\n');

    const emailParams = {
      RawMessage: {
        Data: Buffer.from(rawMessage),
      },
      Source: process.env.SES_MAILER,
    };

    console.log('emailParams -->', emailParams);
    try {
      const ses = new AWS.SES({ region: process.env.REGION });
      console.log('SES client initialized:', ses);
      await ses.sendRawEmail(emailParams).promise();
      console.log('Email with custom headers sent successfully.');
    } catch (err) {
      console.error('Error sending email with headers:', err);
    }
  }

  async saveNotification(notification: NotificationEventDetail): Promise<any> {
    console.log(notification);
    const createdAt = new Date().toISOString();

    const email = {
      PK: notification.email,
      SK: `EMAIL#${createdAt}#${notification.messageDetails.messageId}`,
      sentStatus: true,
      createdAt,
      type: 'EMAIL',
    };

    console.log('email', {
      ...email,
      ...notification,
    });

    await this.dynamoDBService.putObject(
      process.env.APPHERO_NOTIFICATION_TABLE,
      {
        Item: {
          ...email,
          ...notification,
        },
      },
    );

    let inApp;
    if (notification.message) {
      inApp = {
        PK: notification.email,
        SK: `INAPP#${createdAt}#${notification.messageDetails.messageId}`,
        readStatus: false,
        createdAt,
        isClosed: false,
        type: 'INAPP',
      };

      console.log('inapp', {
        ...inApp,
        ...notification,
      });

      // Save the inApp notification
      await this.dynamoDBService.putObject(
        process.env.APPHERO_NOTIFICATION_TABLE,
        {
          Item: {
            ...inApp,
            ...notification,
          },
        },
      );
    }

    return {
      ...(notification.message
        ? { ...inApp, ...notification }
        : { ...email, ...notification }),
    };
  }
  async log(
    correlationId,
    source,
    destination,
    event,
    usecase,
    logMessage,
    secondaryKey,
    entityKey,
    entityKeyField,
    sourcePayload?,
    response?,
    brand?,
    destinationPayload?,
  ) {
    await this.loggerService.log(
      correlationId,
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_BACKEND,
      source,
      destination,
      event,
      usecase,
      sourcePayload || {},
      destinationPayload || {},
      logMessage,
      brand || 'Apphero',
      secondaryKey || '',
      entityKeyField,
      entityKey,
      '',
      '',
      '',
      '',
      response,
    );
  }

  async error(
    correlationId,
    source,
    destination,
    event,
    usecase,
    errorMessage,
    secondaryKey,
    entityKey,
    entityKeyField,
    brand?,
    sourcePayload?,
    destinationPayload?,
  ) {
    await this.loggerService.error(
      correlationId,
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_BACKEND,
      source,
      destination,
      event,
      usecase,
      sourcePayload || {},
      destinationPayload || {},
      errorMessage,
      brand || 'Apphero',
      secondaryKey || '',
      entityKeyField,
      entityKey,
    );
  }

  parseComment = (comment: string) => {
    if (comment?.includes('|')) {
      const [, taskComments] = comment.split('|').map((str) => str.trim());
      return taskComments;
    } else {
      return comment;
    }
  };
}
