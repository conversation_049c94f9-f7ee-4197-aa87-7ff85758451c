import { INotificationContent } from '../INotificationContent';
export class TaskReopen implements INotificationContent {
  inAppMessage: string;
  emailSubject: string;
  emailBody: string;
  constructor() {
    this.inAppMessage =
      'A task previously marked as complete by your educational advisor has been reopened.';
    this.emailSubject =
      'Action Required: Task Reopened in Your AppHero Account';
  }
}
