import { S3Service } from 'apps/src/common/s3.service';
import { INotificationContent } from '../INotificationContent';
export class WelcomeStudentContent implements INotificationContent {
  inAppMessage: string;
  emailSubject: string;
  emailBody: string;

  constructor(private s3Service: S3Service, private brand: string) {
    this.inAppMessage = 'Welcome and thank you for signing up to AppHero!';
    this.emailSubject = `Welcome to AppHero: Your Application Journey Starts Here!`;
  }
}
