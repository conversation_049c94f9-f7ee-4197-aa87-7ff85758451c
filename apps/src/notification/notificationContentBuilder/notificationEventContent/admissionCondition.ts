import { INotificationContent } from '../INotificationContent';
export class AdmissionConditionContent implements INotificationContent {
  inAppMessage: string;
  emailSubject: string;
  emailBody: string;
  constructor() {
    this.inAppMessage =
      'Your {{institution}} {{programName}} application has received an Admissions Condition.';
    this.emailSubject =
      '{{personAccountName}}, Your Admissions Conditions Have Been Updated';
  }
}
