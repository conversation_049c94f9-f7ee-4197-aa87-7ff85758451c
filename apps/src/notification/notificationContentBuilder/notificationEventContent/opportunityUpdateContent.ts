import { INotificationContent } from '../INotificationContent';
export class OpportunityUpdateContent implements INotificationContent {
  inAppMessage: string;
  emailSubject: string;
  emailBody: string;
  constructor() {
    this.inAppMessage =
      'Your {{institution}} application has been updated. Please review the changes';
    this.emailSubject = 'Important Update: Your {{institution}} Application';
  }
}
