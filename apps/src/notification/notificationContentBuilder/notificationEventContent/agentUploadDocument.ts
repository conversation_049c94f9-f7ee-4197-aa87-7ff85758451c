import { INotificationContent } from '../INotificationContent';
export class AgentUploadDocumentContent implements INotificationContent {
  inAppMessage: string;
  emailSubject: string;
  emailBody: string;
  constructor() {
    this.inAppMessage = `Your educational advisor has uploaded a new document to your {{institution}} {{programName}} application.`;
    this.emailSubject = `Alert: New Document Uploaded by Your Educational Advisor`;
  }
}
