import { INotificationContent } from '../INotificationContent';
export class AdmissionLetterDeletion implements INotificationContent {
  inAppMessage: string;
  emailSubject: string;
  emailBody: string;
  constructor() {
    this.inAppMessage =
      'Your recent {{institution}} offer letter has been withdrawn, and your application is still under review.';
    this.emailSubject = 'Important Update: Your {{institution}} Offer Letter';
  }
}
