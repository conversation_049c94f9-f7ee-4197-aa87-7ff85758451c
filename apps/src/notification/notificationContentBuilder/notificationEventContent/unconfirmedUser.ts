import { S3Service } from 'apps/src/common/s3.service';
import { INotificationContent } from '../INotificationContent';
export class UnconfirmedUserContent implements INotificationContent {
  inAppMessage: string;
  emailSubject: string;
  emailBody: string;

  constructor(private s3Service: S3Service, private brand: string) {
    this.emailSubject = `Action Required: Verify Your AppHero Email Now!`;
  }
}
