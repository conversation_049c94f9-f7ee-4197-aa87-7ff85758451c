import { INotificationContent } from '../INotificationContent';
export class IssuedLetterContent implements INotificationContent {
  inAppMessage: string;
  emailSubject: string;
  emailBody: string;
  constructor() {
    this.inAppMessage =
      'You have received a new letter regarding your {{institution}} {{programName}} application.';
    this.emailSubject =
      'Congratulations {{personAccountName}}! Your Admissions Letter is Here';
  }
}
