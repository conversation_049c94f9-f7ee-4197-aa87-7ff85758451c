import { Controller, Post, HttpException, HttpStatus } from '@nestjs/common';
import { OAuthService } from './oauth.service';

export interface TokenResponse {
  access_token: string;
  token_type: string;
  expires_in?: number;
  scope?: string;
  generated_at: string;
}

@Controller('apphero/oauth')
export class OAuthController {
  constructor(private readonly oauthService: OAuthService) {}

  /**
   * Generate OAuth access token
   * POST /apphero/oauth/token
   */
  @Post('token')
  async generateToken(): Promise<TokenResponse> {
    try {
      // Generate access token
      const tokenResponse = await this.oauthService.generateAccessToken();

      return {
        access_token: tokenResponse.access_token,
        token_type: tokenResponse.token_type || 'Bearer',
        expires_in: tokenResponse.expires_in,
        scope: tokenResponse.scope,
        generated_at: new Date().toISOString(),
      };
    } catch (error) {
      console.error('OAuth token generation failed:', error);

      throw new HttpException(
        {
          message: 'Failed to generate OAuth token',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Validate OAuth credentials
   * POST /apphero/oauth/validate
   */
  @Post('validate')
  async validateCredentials(): Promise<{
    valid: boolean;
    parameter_path: string;
  }> {
    try {
      const isValid = await this.oauthService.validateCredentialsAccess();
      const parameterPath = this.oauthService.getParameterName();

      return {
        valid: isValid,
        parameter_path: parameterPath,
      };
    } catch (error) {
      console.error('Credentials validation failed:', error);

      throw new HttpException(
        {
          message: 'Failed to validate OAuth credentials',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
