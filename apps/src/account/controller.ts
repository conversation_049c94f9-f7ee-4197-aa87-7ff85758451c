import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Req,
} from '@nestjs/common';
import { AccountService } from './service';
import { Request } from 'express';

@Controller('apphero')
export class AccountController {
  constructor(private readonly lookUpService: AccountService) {}

  @Patch('/updateAppheroConsent')
  async updateAppheroConsentDetails(
    @Body() accountDetails: any,
    @Req() request: Request,
  ): Promise<any> {
    try {
      return await this.lookUpService.updateAppheroConsentDetails(
        accountDetails,
        request,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('/getPersonAccount/:email')
  async getPersonAccount(
    @Param('email') email: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      return await this.lookUpService.getPersonAccount(email, request);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Patch('/updateAccount/:id')
  async updateAccount(
    @Body() accountDetails: any,
    @Param('id') id: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      return await this.lookUpService.updateAccountDetails(
        accountDetails,
        id,
        request,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
