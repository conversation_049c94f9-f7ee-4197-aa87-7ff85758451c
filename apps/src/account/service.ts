import { Get, Injectable, NotFoundException } from '@nestjs/common';
import { SalesforceService } from '../common/salesforce.service';
import { OptimizedAccountService } from './optimized-account.service';
import { DynamoDBService } from '../common/dynamodb.service';
import { v4 as uuidv4 } from 'uuid';
import { LoggerService } from '../common/cloudwatchService';
import { LoggerEnum } from '@gus-eip/loggers';
const loggerEnum = new LoggerEnum();
@Injectable()
export class AccountService {
  constructor(
    private readonly salesforceService: SalesforceService,
    private readonly optimizedAccountService: OptimizedAccountService,
    private dynamoDBService: DynamoDBService,
    private readonly loggerService: LoggerService,
  ) {}
  async getPersonAccountDetailsFromDb(email) {
    const queryParams = {
      TableName: process.env.APPHERO_SF_ACCOUNT_TABLE,
      KeyConditionExpression: 'PK = :pk and begins_with(SK, :skValue)',
      ExpressionAttributeValues: {
        ':pk': email,
        ':skValue': `${process.env.PERSON_ACCOUNT_RECORDTYPE_ID}`,
      },
    };
    return await this.dynamoDBService.queryObjects(queryParams);
  }

  async savePersonAccountDetailsToDb(details) {
    details.forEach(async (personAccountsDetails) => {
      await this.dynamoDBService.putObject(
        process.env.APPHERO_SF_ACCOUNT_TABLE,
        {
          Item: {
            PK: personAccountsDetails?.PersonEmail,
            SK: `${personAccountsDetails?.RecordTypeId}_${personAccountsDetails?.Id}`,
            ...personAccountsDetails,
            createdAt: new Date().toISOString(),
          },
        },
      );
    });
  }

  async updateAppheroConsentDetails(input, request) {
    // Use optimized account service that bypasses middleware for direct Salesforce calls
    return await this.optimizedAccountService.updateAppheroConsentDetailsOptimized(
      input,
      request,
    );
  }

  async getPersonAccount(email, request?) {
    const requestId = request?.body?.requestId || uuidv4();
    this.log(
      loggerEnum.Event.INITIATED_GET_PERSON_ACCOUNT,
      { email },
      { email },
      'Get person account initiated',
      requestId,
      email,
      'Email__c',
      email,
    );

    try {
      const accountDetails = await this.salesforceService.fetchData(
        `gus/personaccount/${email}`,
      );
      this.log(
        loggerEnum.Event.COMPLETED_GET_PERSON_ACCOUNT,
        { email },
        { email },
        'Get person account completed',
        requestId,
        email,
        'Email__c',
        email,
        accountDetails,
      );
      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        { email },
        { email },
        'Operation completed',
        requestId,
        email,
        'Email__c',
        email,
        accountDetails,
      );
      return accountDetails;
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_GET_PERSON_ACCOUNT,
        { email },
        { email },
        error.message ? error.message : JSON.stringify(error),
        requestId,
        email,
        'Account',
        email,
      );
    }
  }

  async updateAccountDetails(accountDetails, id, request?) {
    const requestId = request?.body?.requestId || uuidv4();
    try {
      this.log(
        loggerEnum.Event.INITIATED_UPDATE_ACCOUNT,
        { accountDetails, id },
        { accountDetails, id },
        'Update account initiated',
        requestId,
        '',
        'accountId',
        id,
      );
      const updatedAccount = await this.salesforceService.patchData(
        'gus/updateAccount',
        { accountDetails, id },
      );
      this.log(
        loggerEnum.Event.COMPLETED_UPDATE_ACCOUNT,
        { accountDetails, id },
        { accountDetails, id },
        'Update account completed',
        requestId,
        '',
        'accountId',
        id,
      );
      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        { accountDetails, id },
        { accountDetails, id },
        'Operation completed',
        requestId,
        '',
        'accountId',
        id,
      );
      return updatedAccount;
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_UPDATE_ACCOUNT,
        { accountDetails, id },
        { accountDetails, id },
        error.message ? error.message : JSON.stringify(error),
        requestId,
        '',
        'Account',
        '',
      );

      throw error;
    }
  }

  async log(
    apiEvent,
    sourcePayload,
    destinationPayload,
    logMessage,
    requestId?,
    email?,
    entityKey?,
    entityKeyField?,
    response?,
  ) {
    await this.loggerService.log(
      requestId || uuidv4(),
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      '',
      sourcePayload,
      destinationPayload,
      logMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }

  async error(
    apiEvent,
    sourcePayload,
    destinationPayload,
    errorMessage,
    requestId?,
    email?,
    entityKey?,
    entityKeyField?,
    response?,
  ) {
    await this.loggerService.error(
      requestId || uuidv4(),
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      '',
      sourcePayload,
      destinationPayload,
      errorMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }
}
