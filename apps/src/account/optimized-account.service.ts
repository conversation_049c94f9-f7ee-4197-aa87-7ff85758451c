import { Injectable, NotFoundException } from '@nestjs/common';
import { DirectSalesforceService } from '../common/direct-salesforce.service';
import { DynamoDBService } from '../common/dynamodb.service';
import { LoggerService } from '../common/cloudwatchService';
import { LoggerEnum } from '@gus-eip/loggers';
import { v4 as uuidv4 } from 'uuid';

const loggerEnum = new LoggerEnum();

@Injectable()
export class OptimizedAccountService {
  constructor(
    private readonly directSalesforceService: DirectSalesforceService,
    private readonly dynamoDBService: DynamoDBService,
    private readonly loggerService: LoggerService,
  ) {}

  /**
   * Optimized method to update AppHero consent using direct Salesforce calls
   * instead of middleware service calls
   */
  async updateAppheroConsentDetailsOptimized(
    input: any,
    request?: any,
  ): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();

    this.log(
      loggerEnum.Event.INITIATED_UPDATE_APPHERO_CONSENT,
      input,
      input,
      'Update apphero consent details initiated (optimized)',
      requestId,
      input.email,
      'Email__c',
      input.email,
    );

    try {
      // Get account info from DB first
      const accountInfo = await this.getPersonAccountDetailsFromDB(input.email);

      if (accountInfo.Items.length !== 0) {
        input.Ids = accountInfo.Items.map((item) => item.Id);
      } else {
        // If no accounts in DB, get from Salesforce directly
        const response =
          await this.directSalesforceService.getPersonAccountByEmail(
            input.email,
          );
        if (response.records && response.records.length > 0) {
          input.Ids = response.records.map((record) => record.Id);
          // Save to DB for future use
          await this.savePersonAccountDetailsToDb(response.records);
        }
      }

      if (!input.Ids || input.Ids.length === 0) {
        throw new NotFoundException(
          `Person Account Not found for this email - ${input.email}`,
        );
      }

      // Use direct Salesforce API call instead of middleware
      const personAccountsDetails =
        await this.directSalesforceService.updateAccount(input);

      if (personAccountsDetails?.alreadyExist) {
        return true;
      }

      if (!personAccountsDetails || personAccountsDetails.length === 0) {
        throw new NotFoundException(
          `Person Account Not found for this email - ${input.email}`,
        );
      }

      // Update the accounts in DB
      await this.updateAccountsInDB(personAccountsDetails, input.email);

      this.log(
        loggerEnum.Event.COMPLETED_UPDATE_APPHERO_CONSENT,
        input,
        personAccountsDetails,
        'Update apphero consent details completed (optimized)',
        requestId,
        input.email,
        'Email__c',
        input.email,
      );

      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        input,
        personAccountsDetails,
        'Operation completed (optimized)',
        requestId,
        input.email,
        'Email__c',
        input.email,
      );

      return true;
    } catch (error) {
      this.log(
        loggerEnum.Event.FAILED_UPDATE_APPHERO_CONSENT,
        input,
        input,
        error.message ? error.message : JSON.stringify(error),
        requestId,
        input.email,
        'Email__c',
        input.email,
      );
      throw error;
    }
  }

  /**
   * Get person account details from DB
   */
  async getPersonAccountDetailsFromDB(email: string): Promise<any> {
    const queryParams = {
      TableName: process.env.APPHERO_SF_ACCOUNT_TABLE,
      KeyConditionExpression: 'PK = :pk and begins_with(SK, :skValue)',
      ExpressionAttributeValues: {
        ':pk': email,
        ':skValue': `${process.env.PERSON_ACCOUNT_RECORDTYPE_ID}`,
      },
    };
    return await this.dynamoDBService.queryObjects(queryParams);
  }

  /**
   * Save person account details to DB
   */
  async savePersonAccountDetailsToDb(details: any[]): Promise<void> {
    const promises = details.map(async (personAccountsDetails) => {
      await this.dynamoDBService.putObject(
        process.env.APPHERO_SF_ACCOUNT_TABLE,
        {
          Item: {
            PK: personAccountsDetails?.PersonEmail,
            SK: `${personAccountsDetails?.RecordTypeId}_${personAccountsDetails?.Id}`,
            ...personAccountsDetails,
            createdAt: new Date().toISOString(),
          },
        },
      );
    });

    await Promise.all(promises);
  }

  /**
   * Update accounts in DB after Salesforce update
   */
  async updateAccountsInDB(updateResults: any[], email: string): Promise<void> {
    const promises = updateResults.map(async (result) => {
      if (result.success) {
        // Update the account in DB with new timestamp
        await this.dynamoDBService.updateObject(
          process.env.APPHERO_SF_ACCOUNT_TABLE,
          {
            PK: email,
            SK: `${process.env.PERSON_ACCOUNT_RECORDTYPE_ID}_${result.id}`,
          },
          {
            updatedAt: new Date().toISOString(),
          },
        );
      }
    });

    await Promise.all(promises);
  }

  /**
   * Log method for tracking optimized operations
   */
  async log(
    apiEvent: string,
    sourcePayload: any,
    destinationPayload: any,
    logMessage: string,
    requestId?: string,
    email?: string,
    entityKey?: string,
    entityKeyField?: string,
    response?: any,
  ) {
    await this.loggerService.log(
      requestId || uuidv4(),
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      '',
      sourcePayload,
      destinationPayload,
      logMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }

  /**
   * Error logging method for optimized operations
   */
  async error(
    apiEvent: string,
    sourcePayload: any,
    destinationPayload: any,
    errorMessage: string,
    requestId?: string,
    email?: string,
    entityKey?: string,
    entityKeyField?: string,
    response?: any,
  ) {
    await this.loggerService.error(
      requestId || uuidv4(),
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      '',
      sourcePayload,
      destinationPayload,
      errorMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }
}
