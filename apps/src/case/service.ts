import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { SalesforceService } from '../common/salesforce.service';
import { LoggerService } from '../common/cloudwatchService';
import { LoggerEnum } from '@gus-eip/loggers';
import { v4 as uuidv4 } from 'uuid';
import { NotificationsService } from '../notification/notification.service';

const loggerEnum = new LoggerEnum();

@Injectable()
export class CaseService {
  constructor(
    private salesforceService: SalesforceService,
    private readonly loggerService: LoggerService,
    private readonly notificationService: NotificationsService,
  ) {}

  async createCase(data: any, requestId: string) {
    try {
      this.validateEmails([data.contactEmailId]);

      const accountDetails = await this.fetchDataOrThrow(
        `gus/personaccountbybrand?email=${data.contactEmailId}&brand=${data.brand}`,
        `No person account found for email - ${data.contactEmailId}`,
      );

      console.log('accountDetails', accountDetails);
      const AccountId = accountDetails.Id;
      const contactId = await this.fetchDataOrThrow(
        `gus/contactbyemail/${data.contactEmailId}?AccountId=${AccountId}`,
        'No contact found for the provided email address.',
      );

      const caseCreationObject = {
        Type: data.type,
        // OwnerId: ownerId?.Id,
        // Assigned_to__c: ownerId?.Id,
        Description: data.description,
        Subject: data.subject,
        Brand__c: data.brand,
        RecordTypeId: process.env.APPHERO_RECORDTYPE_ID,
        ContactId: contactId.Id,
        AccountId: AccountId,
      };

      console.log('caseCreationObject -->', caseCreationObject);

      if (data.opportunityId) {
        (caseCreationObject as any).Opportunity_Id__c = data.opportunityId;
      }

      this.log(
        loggerEnum.Event.INITIATED_CREATE_CASE_RECORD,
        data,
        caseCreationObject,
        'Create case record initiated',
        requestId,
        '',
        'opportunityId',
        data?.opportunityId,
      );

      const caseCreationResponse = await this.salesforceService.postData(
        `gus/case`,
        caseCreationObject,
      );

      console.log('caseCreationResponse -->', caseCreationResponse);

      this.log(
        loggerEnum.Event.COMPLETED_CREATE_CASE_RECORD,
        data,
        caseCreationObject,
        'Create case record completed',
        requestId,
        '',
        'opportunityId',
        data?.opportunityId,
        caseCreationResponse,
      );
      if (caseCreationResponse?.data?.id) {
        console.log(
          'Initiated notification for case creation -->',
          caseCreationResponse?.data?.id,
          data.contactEmailId,
        );
        const response = await this.salesforceService.fetchData(
          `gus/case/${caseCreationResponse.data.id}`,
        );

        if (response.length) {
          const eventName = 'SUPPORT_CASE_CREATED';
          const notificationPayload = {
            messageDetails: {
              messageId: uuidv4(),
              caseNumber: response[0].CaseNumber,
              opportunityId: '',
            },
            email: data.contactEmailId,
            event: eventName as any,
          };
          await this.notificationService.createNotification(
            notificationPayload,
          );
        }
        console.log(
          'Completed notification for case creation -->',
          caseCreationResponse?.data?.id,
          data.contactEmailId,
        );
      }

      return caseCreationResponse;
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_CREATE_CASE_RECORD,
        data,
        data,
        error.message ? error.message : JSON.stringify(error),
        requestId,
        '',
        'opportunityId',
        data?.opportunityId,
      );
      throw error;
    }
  }

  private validateEmails(emails: string[]) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const invalidEmails = emails.filter((email) => !emailRegex.test(email));

    if (invalidEmails.length > 0) {
      const invalidEmailsMessage = invalidEmails.join(', ');
      const errorMessage =
        invalidEmails.length === 1
          ? `Invalid email address: ${invalidEmailsMessage}`
          : `Invalid email addresses: ${invalidEmailsMessage}`;
      throw new BadRequestException(errorMessage);
    }
  }

  private async fetchDataOrThrow(
    url: string,
    errorMessage: string,
  ): Promise<any> {
    console.log('url -->', url);
    const data = await this.salesforceService.fetchData(url);
    console.log('data -->', data);
    if (!data || (Array.isArray(data) && !data.length)) {
      throw new NotFoundException(errorMessage);
    }
    return data;
  }

  async log(
    apiEvent,
    sourcePayload,
    destinationPayload,
    logMessage,
    requestId?,
    email?,
    entityKey?,
    entityKeyField?,
    response?,
  ) {
    await this.loggerService.log(
      requestId,
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      '',
      sourcePayload,
      destinationPayload,
      logMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }

  async error(
    apiEvent,
    sourcePayload,
    destinationPayload,
    errorMessage,
    requestId?,
    email?,
    entityKey?,
    entityKeyField?,
    response?,
  ) {
    await this.loggerService.error(
      requestId,
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      '',
      sourcePayload,
      destinationPayload,
      errorMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }
}
