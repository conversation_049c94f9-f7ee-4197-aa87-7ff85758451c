import {
  Body,
  Controller,
  ForbiddenException,
  HttpException,
  HttpStatus,
  Post,
  Req,
} from '@nestjs/common';
import { CaseService } from './service';
import { Request } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { LoggerEnum } from '@gus-eip/loggers';

const loggerEnum = new LoggerEnum();

@Controller('unauth/apphero')
export class CaseController {
  constructor(private readonly caseService: CaseService) {}

  @Post('case')
  async createCase(@Body() event, @Req() request: Request) {
    const requestId = request?.body?.requestId || uuidv4();
    try {
      const requiredFields = [
        'type',
        'description',
        'brand',
        'fullName',
        'phoneNumber',
        'contactEmailId',
      ];

      const missingOrEmptyFields = [];
      for (let field of requiredFields) {
        if (
          field !== 'agent' &&
          (!event.hasOwnProperty(field) || !event[field])
        ) {
          missingOrEmptyFields.push(field);
        }
      }

      if (missingOrEmptyFields.length > 0) {
        throw new ForbiddenException(
          `The following required fields are either missing or empty: ${missingOrEmptyFields.join(
            ', ',
          )}.`,
        );
      }

      const response = await this.caseService.createCase(event, requestId);
      return response;
    } catch (error) {
      await this.caseService.error(
        loggerEnum.Event.CREATE_CASE_RECORD,
        event,
        event,
        error,
        requestId,
        event?.contactEmailId,
        'opportunityId',
        event?.opportunityId,
      );
      throw error;
    }
  }
}
