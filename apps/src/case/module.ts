import { Module } from '@nestjs/common';
import { CaseController } from './controller';
import { CaseService } from './service';
import { SalesforceService } from '../common/salesforce.service';
import { LoggerService } from '../common/cloudwatchService';
import { NotificationModule } from '../notification/module';

@Module({
  controllers: [CaseController],
  providers: [CaseService, SalesforceService, LoggerService],
  exports: [CaseService],
  imports: [NotificationModule],
})
export class CaseModule {}
