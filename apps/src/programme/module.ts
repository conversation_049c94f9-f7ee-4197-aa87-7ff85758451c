import { Module } from '@nestjs/common';
import { ProgrammeController } from './controller';
import { ProgrammeService } from './service';
import { CommonModule } from '../common/module';
import { LoggerModule } from '@gus-eip/loggers';
@Module({
  imports: [
    CommonModule
  ],
  controllers: [ProgrammeController],
  providers: [ProgrammeController, ProgrammeService],
  exports: [ProgrammeController],
})
export class ProgrammeModule { }
