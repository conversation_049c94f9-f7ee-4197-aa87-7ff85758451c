import {
  Controller,
  Get,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Query,
  Delete,
  Req,
} from '@nestjs/common';
import { ProgrammeService } from './service';
import { Request } from 'express';

@Controller('/apphero')
export class ProgrammeController {
  constructor(private readonly programmeService: ProgrammeService) { }
  @Post('/programmes/list')
  async listPrograms(@Body() requestBody, @Req() request: Request): Promise<any> {
    try {
      return await this.programmeService.getProgrammeList(requestBody, request);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/compareprogrammes')
  async compareProgramme(@Query() event: any, @Req() request: Request): Promise<any> {
    return await this.programmeService.compareProgramByIds(event);
  }
  // // id's can be multiple and brand's also multiple
  @Get('/programme/compare')
  async comparePrograms(@Query() event: any, @Req() request: Request): Promise<any> {
    return await this.programmeService.compareProgramById(event, request);
  }

  @Get('/programmes/:programmeId')
  async programDetails(
    @Param('programmeId') programmeId: string,
    @Req() request: Request
  ): Promise<any> {
    return await this.programmeService.programmeDetails(programmeId, request);
  }
  @Get('/programmes/:programmeId/:brand')
  async programmeDetailsByBrand(
    @Param('programmeId') programmeId: string,
    @Param('brand') brand: string,
    @Req() request: Request
  ): Promise<any> {
    return await this.programmeService.programmeDetailsByIdAndBrand(
      programmeId,
      brand,
      request
    );
  }
  @Get('/pricebookdetailsbyprogrammeid/:programmeId')
  async programPriceBookDetails(
    @Param('programmeId') programmeId: string,
    @Req() request: Request
  ): Promise<any> {
    return await this.programmeService.programmePriceBookDetails(programmeId, request);
  }
  @Get('/pricebookentries/:programmeId/:brand')
  async getPriceBookEntriesByProgramme(
    @Param('programmeId') programmeId: string,
    @Param('brand') brand: string,
    @Req() request: Request
  ): Promise<any> {
    return await this.programmeService.getPriceBookEntriesById(
      programmeId,
      brand,
      request
    );
  }
  @Post('/programmeinstitutions')
  async getAllInstitutions(@Body() event, @Req() request: Request): Promise<any> {
    console.log('Controller Request ==>', event);
    return await this.programmeService.programbyInstitution(event, request);
  }
  @Post('/application/basket')
  async addApplication(@Body() requestBody, @Req() request: Request): Promise<any> {
    try {
      return await this.programmeService.addApplicationToBasket(requestBody);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/application/basket/:email')
  async applicationBasketDetails(@Param('email') email: string, @Req() request: Request): Promise<any> {
    return await this.programmeService.getApplicationBasketByEmail(email, request);
  }
  @Delete('/application/basket')
  async deleteConsumer(
    @Query('email') email: string,
    @Query('externalId') externalId?: boolean,
    @Req() request?: Request
  ): Promise<any> {
    return await this.programmeService.deleteApplicationFromBasket(
      email,
      externalId,
      request
    );
  }
  @Post('/programme/compare/basket')
  async addProgramme(@Body() requestBody, @Req() request: Request): Promise<any> {
    try {
      return await this.programmeService.addProgrammeToBasket(requestBody, request);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/programme/compare/basket/:email')
  async compareBasketDetails(@Param('email') email: string, @Req() request: Request): Promise<any> {
    return await this.programmeService.getProgrammeBasketByEmail(email, request);
  }
}
