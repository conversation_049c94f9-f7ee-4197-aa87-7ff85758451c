import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { SalesforceService } from '../common/salesforce.service';
import { DynamoDBService } from '../common/dynamodb.service';
import { v4 as uuidv4 } from 'uuid';
import { LoggerService } from '../common/cloudwatchService';
import { LoggerEnum } from '@gus-eip/loggers';
const loggerEnum = new LoggerEnum();
@Injectable()
export class ProgrammeService {
  constructor(
    private readonly dynamoDBService: DynamoDBService,
    private readonly salesforceService: SalesforceService,
    private readonly loggerService: LoggerService,
  ) {}
  async getProgrammeList(event, request?): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];
    try {
      this.log(
        loggerEnum.Event.INITIATED_LIST_PROGRAMMES,
        event,
        event,
        'Get programme list initiated',
        requestId,
        '',
        'Programme',
        '',
        null,
        usecase,
      );
      const { size, from, filters } = event.filterQuery;
      let brandNames = [];

      if (filters?.Institution && filters.Institution?.values) {
        filters.Institution.values.forEach((values) => {
          brandNames.push(values);
        });
      } else {
        brandNames = await this.dynamoDBService.getAppheroSupportedBrand();
      }
      let filterParameters,
        isfilter = false;
      if (filters && (filters?.Subject?.values || filters?.Level?.values)) {
        filterParameters = await this.programFilterExpression(filters);
        isfilter = true;
      }
      let params;
      const queryPromises = brandNames.map(async (brandName) => {
        params = {
          TableName: `apphero-sf-programme-${process.env.STAGE}`,
          KeyConditionExpression: 'PK = :pkValue',
          ProjectionExpression:
            '#brandName__c, #name, #schoolName__c, #id, #level_Name__c, #subjectName, #currencyIsoCode, #duration__c, #location__c, #productid, #intake',
          ExpressionAttributeNames: {
            '#brandName__c': 'BrandName__c',
            '#name': 'Name',
            '#schoolName__c': 'SchoolName__c',
            '#id': 'Id',
            '#level_Name__c': 'Level_Name__c',
            '#subjectName': 'Subject__r.Name',
            '#currencyIsoCode': 'CurrencyIsoCode',
            '#duration__c': 'product2.Duration__c',
            '#location__c': 'product2.Location__c',
            '#productid': 'product2.Id',
            '#intake': 'product2.Intake__c',
          },
          ExpressionAttributeValues: { ':pkValue': brandName },
        };

        if (isfilter) {
          params.ExpressionAttributeValues = {
            ...params.ExpressionAttributeValues,
            ...filterParameters.ExpressionAttributeValues,
          };
          params.ExpressionAttributeNames = {
            ...params.ExpressionAttributeNames,
            ...filterParameters.ExpressionAttributeNames,
          };

          params['FilterExpression'] = filterParameters.FilterExpression;
        }
        return await this.dynamoDBService.queryObjects(params);
      });
      const results = await Promise.all(queryPromises);
      let programs = [].concat.apply(
        [],
        results.map((result) => result.Items || results),
      );
      if (filters?.Intake && filters?.Intake?.values) {
        const targetDates = this.convertToTargetFormat(filters?.Intake?.values);
        programs = programs.filter((program) =>
          this.includesTargetIntake(program['product2.Intake__c'], targetDates),
        );
      }
      const response = {
        total: programs.length,
        programList: programs
          ? await this.processPrograms(programs.slice(from, from + size))
          : [],
      };

      this.log(
        loggerEnum.Event.COMPLETED_LIST_PROGRAMMES,
        event,
        event,
        'Get programme list completed',
        requestId,
        '',
        'Programme',
        '',
        response,
        usecase,
      );
      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        event,
        event,
        'Operation Completed',
        requestId,
        '',
        'Programme',
        '',
        response,
        usecase,
      );
      return response;
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_LIST_PROGRAMMES,
        event,
        event,
        error.message ? error.message : JSON.stringify(error),
        requestId,
        '',
        'Programme',
        '',
        null,
        usecase,
      );
      throw error;
    }
  }
  includesTargetIntake = (intakes, targetDates) => {
    const filteredList = intakes?.filter((item) => item !== null);
    if (filteredList && filteredList !== null && filteredList.length > 0) {
      return filteredList.some((intake) => {
        return targetDates.some((targetDate) => intake.includes(targetDate));
      });
    } else {
      return false;
    }
  };

  async programbyInstitution(event, request) {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];
    try {
      this.log(
        loggerEnum.Event.INITIATED_GET_ALL_INSTITUTIONS,
        event,
        event,
        'Get all institutions initiated',
        requestId,
        '',
        'Programme',
        '',
        null,
        usecase,
      );
      const { size, from, filters } = event.filterQuery;
      let brandNames;
      if (filters && filters?.Institution?.values) {
        brandNames = filters?.Institution?.values;
      } else {
        brandNames = await this.dynamoDBService.getAppheroSupportedBrand();
      }
      let filterParameters,
        isfilter = false;
      if (filters && (filters?.Subject?.values || filters?.Level?.values)) {
        filterParameters = await this.programFilterExpression(filters);
        isfilter = true;
      }
      const resultList = [];

      let params;
      const queryPromises = brandNames.map(async (brandName) => {
        params = {
          TableName: process.env.APPHERO_PROGRAMME_TABLE,
          KeyConditionExpression: 'PK = :pkValue',
          ProjectionExpression: '#brandName__c,#schoolName__c,#intake',
          ExpressionAttributeNames: {
            '#brandName__c': 'BrandName__c',
            '#schoolName__c': 'SchoolName__c',
            '#intake': 'product2.Intake__c',
          },
          ExpressionAttributeValues: { ':pkValue': brandName },
        };
        if (isfilter) {
          params.ExpressionAttributeValues = {
            ...params.ExpressionAttributeValues,
            ...filterParameters.ExpressionAttributeValues,
          };
          params.ExpressionAttributeNames = {
            ...params.ExpressionAttributeNames,
            ...filterParameters.ExpressionAttributeNames,
          };

          params['FilterExpression'] = filterParameters.FilterExpression;
        }

        return await this.dynamoDBService.queryObjects(params);
      });

      const results = await Promise.all(queryPromises);
      let intakeFilter = false;
      let targetDates;
      if (filters?.Intake && filters?.Intake?.values) {
        targetDates = this.convertToTargetFormat(filters?.Intake?.values);
        intakeFilter = true;
      }
      for (const result of results) {
        let programs = result['Items'];
        let count = result['Count'];
        if (intakeFilter && result) {
          programs = programs.filter((program) =>
            this.includesTargetIntake(
              program['product2.Intake__c'],
              targetDates,
            ),
          );
          count = programs.length;
        }
        if (count !== 0) {
          resultList.push({
            total: count,
            brandFullName: programs[0]?.SchoolName__c || '',
            Brand__c: programs[0]?.BrandName__c || '',
          });
        }
      }

      this.log(
        loggerEnum.Event.COMPLETED_GET_ALL_INSTITUTIONS,
        event,
        event,
        'Get all institutions completed',
        requestId,
        '',
        'Programme',
        '',
        {
          institutions: resultList.slice(from, from + size),
        },
        usecase,
      );
      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        event,
        event,
        'Operation completed',
        requestId,
        '',
        'Programme',
        '',
        {
          institutions: resultList.slice(from, from + size),
        },
        usecase,
      );
      return {
        institutions: resultList.slice(from, from + size),
      };
    } catch (error) {
      this.log(
        loggerEnum.Event.FAILED_GET_ALL_INSTITUTIONS,
        event,
        event,
        error.message ? error.message : JSON.stringify(error),
        requestId,
        '',
        'Programme',
        '',
        null,
        usecase,
      );
      throw error;
    }
  }
  async programFilterExpression(filters) {
    const params = {
      ExpressionAttributeNames: {},
      ExpressionAttributeValues: {},
      filterSubjectExpressions: [],
      filterLevelExpressions: [],
    };
    if (filters && filters?.Level?.values) {
      filters.Level.values.forEach((levelValue, index) => {
        const levelNameAttribute = `#level_Name${index}`;
        params.filterLevelExpressions.push(
          `contains(${levelNameAttribute}, :level${index})`,
        );
        params.ExpressionAttributeNames[levelNameAttribute] = 'Level_Name__c';
        params.ExpressionAttributeValues[`:level${index}`] = levelValue;
      });
    }

    if (filters && filters?.Subject?.values) {
      filters.Subject.values.forEach((subjectValue, index) => {
        const subjectNameAttribute = `#subjectName${index}`;
        params.filterSubjectExpressions.push(
          `contains(${subjectNameAttribute}, :subject${index})`,
        );
        params.ExpressionAttributeNames[subjectNameAttribute] =
          'Subject__r.Name';
        params.ExpressionAttributeValues[`:subject${index}`] = subjectValue;
      });
    }

    if (
      params.filterSubjectExpressions.length > 0 &&
      params.filterLevelExpressions.length > 0
    ) {
      params['FilterExpression'] = `${params.filterSubjectExpressions.join(
        ' OR ',
      )} AND ${params.filterLevelExpressions.join(' OR ')}`;
    } else if (params.filterSubjectExpressions.length > 0) {
      params['FilterExpression'] = params.filterSubjectExpressions.join(' OR ');
    } else if (params.filterLevelExpressions.length > 0) {
      params['FilterExpression'] = params.filterLevelExpressions.join(' OR ');
    }
    return params;
  }
  async batchGetMaxOrMinPrices(keys, min = false) {
    const tableName = `apphero-sf-product2-${process.env.STAGE}`;
    const params = {
      RequestItems: {
        [`${tableName}`]: {
          Keys: keys,
          ProjectionExpression: '#unitPrice',
          ExpressionAttributeNames: {
            '#unitPrice': 'pricebookentry.UnitPrice',
          },
        },
      },
    };

    try {
      const data = await this.dynamoDBService.batchGet(params);
      const mergedPrices = data.Responses[`${tableName}`].reduce(
        (accumulator, item) => {
          const unitPrice = item['pricebookentry.UnitPrice'];
          if (Array.isArray(unitPrice)) {
            accumulator.push(
              ...unitPrice.filter((price) => typeof price === 'number'),
            );
          }
          return accumulator;
        },
        [],
      );

      if (Array.isArray(mergedPrices) && mergedPrices.length > 0) {
        return min ? Math.min(...mergedPrices) : Math.max(...mergedPrices);
      } else {
        return null;
      }
    } catch (error) {
      console.error('Error retrieving product details:', error);
      throw error;
    }
  }

  async processPrograms(programs) {
    const programDetailsMappings = {
      SchoolName__c: 'brandFullName',
      BrandName__c: 'brand',
      'product2.Location__c': 'locations',
      Level_Name__c: 'level',
      Id: 'id',
      CurrencyIsoCode: 'currencyCode',
      Name: 'name',
    };

    const response = [];

    for (const program of programs) {
      const mappedData = {};

      for (const [key, mappedKey] of Object.entries(programDetailsMappings)) {
        mappedData[mappedKey] = program[key];
      }

      const durations = program['product2.Duration__c'] || [];
      mappedData['maxDuration'] = Math.max(...durations);
      mappedData['minDuration'] = Math.min(...durations);

      const brandName = program.BrandName__c;
      const productIds = program['product2.Id'] || [];
      const id = program['Id'];
      if (productIds.length > 0) {
        const keys = productIds.map((productId) => ({
          PK: brandName,
          SK: `${id}_${productId}`,
        }));

        mappedData['price'] = await this.batchGetMaxOrMinPrices(keys);
      } else {
        mappedData['price'] = [];
      }

      response.push(mappedData);
    }

    return response;
  }

  async compareProgramByIds(event, request?): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];
    try {
      await this.log(
        loggerEnum.Event.COMPARE_PROGRAMME_BY_IDS,
        event,
        event,
        'Compare program by id initiated',
        requestId,
        '',
        'Programme',
        '',
        null,
        usecase,
      );
      const keys = event?.ids.split(',').map((ele) => {
        const [PK, SK] = ele.split('_');
        return { PK, SK };
      });
      const tableName = process.env.APPHERO_PROGRAMME_TABLE;

      const params = {
        RequestItems: {
          [`${tableName}`]: {
            Keys: keys,
            ProjectionExpression:
              '#program,#product2Id,#active,#brand,#institutionCurrencyIsoCode,#id,#level,#Name,#delivery,#institution,#duration,#subject,#location__c',
            ExpressionAttributeNames: {
              '#id': 'Id',
              '#location__c': 'product2.Location__c',
              '#program': 'product2.Name',
              '#active': 'Active__c',
              '#brand': 'BrandName__c',
              '#institutionCurrencyIsoCode': 'CurrencyIsoCode',
              '#level': 'Level_Name__c',
              '#Name': 'Name',
              '#delivery': 'product2.Delivery__r',
              '#duration': 'product2.Duration__c',
              '#institution': 'SchoolName__c',
              '#subject': 'Subject__r.Name',
              '#product2Id': 'product2.Id',
            },
          },
        },
      };

      const data = await this.dynamoDBService.batchGet(params);
      const programDetailsMappings = {
        SchoolName__c: 'institution',
        BrandName__c: 'brand',
        'product2.Location__c': 'location',
        Level_Name__c: 'level',
        Id: 'Id',
        CurrencyIsoCode: 'institutionCurrencyIsoCode',
        Name: 'Name',
        'Subject__r.Name': 'subject',
        'product2.Delivery__r': 'delivery',
      };

      const programData = data?.Responses?.[tableName];
      const response = [];
      for (const program of programData) {
        const mappedData = {};

        for (const [key, mappedKey] of Object.entries(programDetailsMappings)) {
          mappedData[mappedKey] = program[key];
        }

        const durations = program['product2.Duration__c'] || [];
        mappedData['maxDuration'] = Math.max(...durations);
        mappedData['minDuration'] = Math.min(...durations);

        const brandName = program.BrandName__c;
        const productIds = program['product2.Id'] || [];
        const id = program['Id'];
        if (productIds.length > 0) {
          const keys = productIds.map((productId) => ({
            PK: brandName,
            SK: `${id}_${productId}`,
          }));
          mappedData['maxFees'] = await this.batchGetMaxOrMinPrices(keys); // max
          mappedData['minFees'] = await this.batchGetMaxOrMinPrices(keys, true); //min
        } else {
          mappedData['maxFees'] = null;
          mappedData['minFees'] = null;
        }
        const distinctLocations = Array.from(new Set(mappedData['location']));
        const online = distinctLocations?.includes('Online');
        const filteredLocations = distinctLocations?.filter(
          (location) => location !== 'Online',
        );
        mappedData['online'] = online;
        mappedData['location'] = filteredLocations;
        response.push(mappedData);
      }

      await this.log(
        loggerEnum.Event.COMPARE_PROGRAMME_BY_IDS,
        event,
        event,
        'Compare programs by id completed',
        requestId,
        '',
        'Programme',
        '',
        response,
        usecase,
      );
      return response;
    } catch (error) {
      await this.error(
        loggerEnum.Event.COMPARE_PROGRAMME_BY_IDS,
        event,
        event,
        error.message ? error.message : JSON.stringify(error),
        requestId,
        '',
        'Programme',
        '',
        null,
        usecase,
      );
      throw error;
    }
  }
  async compareProgramById(event, request?): Promise<any> {
    const requestId = request.body.requestId;
    try {
      this.log(
        loggerEnum.Event.INITIATED_COMPARE_PROGRAMME_BY_IDS,
        event,
        event,
        'Compare program by id initiated',
        requestId,
        '',
        'Programme',
        '',
      );
      const response = await this.salesforceService.fetchData(
        `gus/compareprogrammes?ids=${event?.ids}`,
      );

      this.log(
        loggerEnum.Event.COMPLETED_COMPARE_PROGRAMME_BY_IDS,
        event,
        event,
        'Compare program by id completed',
        requestId,
        '',
        'Programme',
        '',
        response,
      );
      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        event,
        event,
        'Operation completed',
        requestId,
        '',
        'Programme',
        '',
        response,
      );
      return response;
    } catch (error) {
      this.log(
        loggerEnum.Event.FAILED_COMPARE_PROGRAMME_BY_IDS,
        event,
        event,
        error.message ? error.message : JSON.stringify(error),
        requestId,
        '',
        'Programme',
        '',
      );
      throw error;
    }
  }

  async programmeDetails(programmeId, request?): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();
    try {
      await this.log(
        loggerEnum.Event.GET_PROGRAMME_DETAILS_BY_ID,
        { programmeId },
        { programmeId },
        'Get programme details by programmeid initiated',
        requestId,
        '',
        'Programme',
        programmeId,
      );
      const response = await this.salesforceService.fetchData(
        `gus/getprogramme/${programmeId}`,
      );
      await this.log(
        loggerEnum.Event.GET_PROGRAMME_DETAILS_BY_ID,
        { programmeId },
        { programmeId },
        'Get programme details by programmeid completed',
        requestId,
        '',
        'Programme',
        programmeId,
        response,
      );

      return response;
    } catch (error) {
      await this.error(
        loggerEnum.Event.GET_PROGRAMME_DETAILS_BY_ID,
        { programmeId },
        { programmeId },
        error.message ? error.message : JSON.stringify(error),
        requestId,
        '',
        'Programme',
        programmeId,
      );
      throw error;
    }
  }

  async programmeDetailsByIdAndBrand(programmeId, brand, request) {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];
    try {
      const param = {
        TableName: process.env.APPHERO_PROGRAMME_TABLE,
        KeyConditionExpression: 'PK = :brand and SK = :id',
        ExpressionAttributeValues: {
          ':id': programmeId,
          ':brand': brand,
        },
        ProjectionExpression:
          '#brandName__c,#id,#schoolName__c,#name,#subjectName,#location,#durationText,#delivery,#programmeDescription,#levelName',
        ExpressionAttributeNames: {
          '#brandName__c': 'BrandName__c',
          '#id': 'Id',
          '#schoolName__c': 'SchoolName__c',
          '#name': 'Name',
          '#levelName': 'Level_Name__c',
          '#subjectName': 'Subject__r.Name',
          '#location': 'product2.Location__c',
          '#durationText': 'product2.DurationText__c',
          '#delivery': 'product2.Delivery__r',
          '#programmeDescription': 'Programme_Description__c',
        },
      };

      this.log(
        loggerEnum.Event.INITIATED_PROGRAMMES_BY_ID_AND_BRAND,
        { programmeId, brand },
        param,
        'get program details by id and brand initiated',
        requestId,
        '',
        'Programme',
        programmeId,
        null,
        usecase,
      );
      const programmeDetails = await this.dynamoDBService.queryObjects(param);

      this.log(
        loggerEnum.Event.FETCHED_PROGRAMME_DETAILS_BY_ID_AND_BRAND,
        { programmeId, brand },
        param,
        'Program details by id and brand',
        requestId,
        '',
        'Programme',
        programmeId,
        programmeDetails.Items,
        usecase,
      );
      const details = {
        Id: programmeDetails.Items[0]?.Id,
        brand: programmeDetails.Items[0]?.BrandName__c,
        brandFullName: programmeDetails.Items[0]?.SchoolName__c,
        name: programmeDetails.Items[0].Name,
        level: programmeDetails.Items[0]?.Level_Name__c,
        subject: programmeDetails.Items[0]?.['Subject__r.Name'],
        location: programmeDetails.Items[0]?.['product2.Location__c'],
        duration: programmeDetails.Items[0]?.['product2.DurationText__c'],
        delivery: programmeDetails.Items[0]?.['product2.Delivery__r'],
        description: programmeDetails.Items[0]?.Programme_Description__c,
      };

      const params = {
        TableName: process.env.APPHERO_LOOKUP_TABLE,
        KeyConditionExpression: 'PK = :pkValue',
        FilterExpression: '#brandName = :brandName',
        ExpressionAttributeNames: {
          '#brandName': 'Brands__c',
        },
        ExpressionAttributeValues: {
          ':pkValue': 'BusinessUnit',
          ':brandName': programmeDetails.Items[0].BrandName__c,
        },
      };

      const uspDetails = await this.dynamoDBService.queryObjects(params);

      const programmeDetailsWithUSP = {
        ...details,
        usp1Title: uspDetails.Items[0]?.USP_1_Title__c,
        usp1Description: uspDetails.Items[0]?.USP_1_Description__c,
        usp2Title: uspDetails.Items[0]?.USP_2_Title__c,
        usp2Description: uspDetails.Items[0]?.USP_2_Description__c,
        usp3Title: uspDetails.Items[0]?.USP_3_Title__c,
        usp3Description: uspDetails.Items[0]?.USP_3_Description__c,
        uspVideoLink: uspDetails.Items[0]?.USP_Video_Link__c,
      };

      this.log(
        loggerEnum.Event.COMPLETED_PROGRAMMES_BY_ID_AND_BRAND,
        { programmeId, brand },
        param,
        'Get Program details by id and brand completed',
        requestId,
        '',
        'Programme',
        programmeId,
        programmeDetailsWithUSP,
        usecase,
      );
      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        { programmeId, brand },
        param,
        'Operation completed',
        requestId,
        '',
        'Programme',
        programmeId,
        programmeDetailsWithUSP,
        usecase,
      );
      return programmeDetailsWithUSP;
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_PROGRAMMES_BY_ID_AND_BRAND,
        { programmeId, brand },
        { programmeId, brand },
        error.message ? error.message : JSON.stringify(error),
        requestId,
        '',
        'Programme',
        programmeId,
        null,
        usecase,
      );

      throw error;
    }
  }
  async programmePriceBookDetails(programmeId, request?): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();
    try {
      await this.log(
        loggerEnum.Event.GET_PRICEBOOK_ENTRIES_BY_PROGRAMMEID,
        { programmeId },
        { programmeId },
        'Get pricebook details by programmeId initiated',
        requestId,
        '',
        'Programme',
        programmeId,
      );

      const response = await this.salesforceService.fetchData(
        `gus/getpricebookdetails/${programmeId} `,
      );

      await this.log(
        loggerEnum.Event.GET_PRICEBOOK_ENTRIES_BY_PROGRAMMEID,
        { programmeId },
        { programmeId },
        'Get pricebook details by programmeId completed',
        requestId,
        '',
        'Programme',
        programmeId,
      );
      return response;
    } catch (error) {
      await this.error(
        loggerEnum.Event.GET_PRICEBOOK_ENTRIES_BY_PROGRAMMEID,
        { programmeId },
        { programmeId },
        error.message ? error.message : JSON.stringify(error),
        requestId,
        '',
        'Programme',
        programmeId,
      );
      throw error;
    }
  }
  async getPriceBookEntriesById(programmeId, brand, request?): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];
    try {
      const priceBookEntriesMapping = {
        Location__c: 'location',
        DurationText__c: 'duration',
        Intake__c: 'intakeDate',
        UnitPrice: 'price',
        'Pricebook2.Name': 'priceBookName',
        CurrencyIsoCode: 'currencyCode',
      };
      const priceBookEntryList = [];
      this.log(
        loggerEnum.Event.INITIATED_PRICEBOOK_ENTRIES_BY_ID_AND_BRAND,
        { programmeId, brand },
        priceBookEntriesMapping,
        'Get pricebook entries by id and brand initiated',
        requestId,
        '',
        'Programme',
        programmeId,
        null,
        usecase,
      );
      const programmeDetails = await this.dynamoDBService.getObject(
        `apphero-sf-programme-${process.env.STAGE}`,
        {
          PK: brand,
          SK: programmeId,
        },
        { ProjectionExpression: '#product2Id' },
        { ExpressionAttributeNames: { '#product2Id': 'product2.Id' } },
      );
      if (
        programmeDetails &&
        programmeDetails.Item &&
        Object.keys(programmeDetails.Item).length === 0
      ) {
        await this.log(
          loggerEnum.Event.NOT_FOUND_PRICEBOOK_ENTRIES,
          { programmeId, brand },
          priceBookEntriesMapping,
          'No pricebook entries found',
          requestId,
          '',
          'Programme',
          programmeId,
          {
            priceEntries: [],
            total: 0,
          },
          usecase,
        );

        return {
          priceEntries: [],
          total: 0,
        };
      }
      for (const productId of programmeDetails?.Item?.['product2.Id']) {
        const productDetails = await this.dynamoDBService.getObject(
          `apphero-sf-product2-${process.env.STAGE}`,
          {
            PK: brand,
            SK: `${programmeId}_${productId}`,
          },
          {
            ProjectionExpression:
              '#pricebookentryId,#location__c,#durationText__c,#intake__c',
          },
          {
            ExpressionAttributeNames: {
              '#pricebookentryId': 'pricebookentry.Id',
              '#location__c': 'Location__c',
              '#durationText__c': 'DurationText__c',
              '#intake__c': 'Intake__c',
            },
          },
        );
        for (const pricebookentryId of productDetails?.Item?.[
          'pricebookentry.Id'
        ]) {
          const getPricebookEntriesDetails =
            await this.dynamoDBService.getObject(
              `apphero-sf-pricebookentry-${process.env.STAGE}`,
              {
                PK: brand,
                SK: `${productId}_${pricebookentryId}`,
              },
              {
                ProjectionExpression:
                  '#unitPrice,#pricebook2Name,#currencyIsoCode',
              },
              {
                ExpressionAttributeNames: {
                  '#unitPrice': 'UnitPrice',
                  '#pricebook2Name': 'Pricebook2.Name',
                  '#currencyIsoCode': 'CurrencyIsoCode',
                },
              },
            );
          const priceBookEntries = {
            ...getPricebookEntriesDetails.Item,
            ...productDetails.Item,
          };
          const mappedData = {};
          for (const [key, mappedKey] of Object.entries(
            priceBookEntriesMapping,
          )) {
            mappedData[mappedKey] = priceBookEntries[key];
          }
          priceBookEntryList.push(mappedData);
        }

        this.log(
          loggerEnum.Event.COMPLETED_PRICEBOOK_ENTRIES_BY_ID_AND_BRAND,
          { programmeId, brand },
          priceBookEntriesMapping,
          'Get pricebook entries by id and brand completed',
          requestId,
          '',
          'Programme',
          programmeId,
          {
            priceEntries: priceBookEntryList,
            total: priceBookEntryList.length,
          },
          usecase,
        );
        this.log(
          loggerEnum.Event.OPERATION_COMPLETED,
          { programmeId, brand },
          priceBookEntriesMapping,
          'Operation completed',
          requestId,
          '',
          'Programme',
          programmeId,
          {
            priceEntries: priceBookEntryList,
            total: priceBookEntryList.length,
          },
          usecase,
        );
      }
      return {
        priceEntries: priceBookEntryList,
        total: priceBookEntryList.length,
      };
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_PRICEBOOK_ENTRIES_BY_ID_AND_BRAND,
        { programmeId, brand },
        { programmeId, brand },
        error.message ? error.message : JSON.stringify(error),
        requestId,
        '',
        'Programme',
        programmeId,
        null,
        usecase,
      );
      throw error;
    }
  }
  async getApplicationBasketByEmail(email, request?): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];
    try {
      const params = {
        TableName: process.env.APPHERO_APPLICATION_BASKET_TABLE,
        KeyConditionExpression: 'PK = :pkValue',
        ExpressionAttributeValues: {
          ':pkValue': email,
        },
      };
      this.log(
        loggerEnum.Event.INITIATED_APPLICATION_BASKET_BY_EMAIL,
        { email },
        { email },
        'Get application basket by email initiated',
        requestId,
        email,
        'Application',
        '',
        null,
        usecase,
      );
      const response = await this.dynamoDBService.queryObjects(params);
      const sortedresponse = response?.Items.sort((a, b) => {
        const dateA = new Date(a.dateTime);
        const dateB = new Date(b.dateTime);
        return dateA.getTime() - dateB.getTime();
      });
      this.log(
        loggerEnum.Event.COMPLETED_APPLICATION_BASKET_BY_EMAIL,
        { email },
        { email },
        'Get application basket by email completed',
        requestId,
        email,
        'Application',
        '',
        sortedresponse,
        usecase,
      );
      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        { email },
        { email },
        'Operation completed',
        requestId,
        email,
        'Application',
        '',
        sortedresponse,
        usecase,
      );
      return sortedresponse;
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_APPLICATION_BASKET_BY_EMAIL,
        { email },
        { email },
        error.message ? error.message : JSON.stringify(error),
        requestId,
        email,
        'Application',
        '',
        null,
        usecase,
      );

      throw error;
    }
  }
  async addApplicationToBasket(applicationDetails): Promise<any> {
    try {
      const externalId = uuidv4();
      const PK = applicationDetails.email;
      const SK = externalId;
      const dateTime = new Date().toISOString();
      applicationDetails['dateTime'] = dateTime;
      if (!applicationDetails.bucketId) {
        const bucketId = uuidv4();
        applicationDetails['bucketId'] = bucketId;
      } else {
        const basketDetails = await this.getApplicationBasketByEmail(
          applicationDetails.email,
        );
        const isBrandPresent = basketDetails.some(
          (application) => application.brand === applicationDetails?.brand,
        );
        if (isBrandPresent) {
          throw new HttpException(
            'Programme with this brand already exist in the basket',
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }
      }
      const createApplicationParams: AWS.DynamoDB.PutItemInput = {
        TableName: process.env.APPHERO_APPLICATION_BASKET_TABLE,
        Item: {
          PK: PK,
          SK: SK,
          ...applicationDetails,
        },
      };
      await this.dynamoDBService.putObject(
        process.env.APPHERO_APPLICATION_BASKET_TABLE,
        {
          Item: {
            PK: PK,
            SK: SK,
            ...applicationDetails,
          },
        },
      );
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
  async deleteApplicationFromBasket(email, externalId, request): Promise<any> {
    const requestId = request.body.requestId || uuidv4();
    try {
      this.log(
        loggerEnum.Event.INITIATED_DELETE_APPLICATION_FROM_BASKET,
        { email, externalId },
        { email, externalId },
        'Delete application from basket initiated',
        requestId,
        email,
        'Application',
        externalId,
      );
      const deleteDocumentParams = {
        TableName: process.env.APPHERO_APPLICATION_BASKET_TABLE,
        Key: {
          PK: email,
          SK: externalId,
        },
      };

      await this.dynamoDBService.deleteObject(deleteDocumentParams);

      this.log(
        loggerEnum.Event.COMPLETED_DELETE_APPLICATION_FROM_BASKET,
        { email, externalId },
        { email, externalId },
        'Delete application from basket completed',
        requestId,
        email,
        'Application',
        externalId,
      );

      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        { email, externalId },
        { email, externalId },
        'Operation completed',
        requestId,
        email,
        'Application',
        externalId,
      );
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_DELETE_APPLICATION_FROM_BASKET,
        { email, externalId },
        { email, externalId },
        error.message ? error.message : JSON.stringify(error),
        requestId,
        email,
        'Application',
        externalId,
      );
      throw error;
    }
  }
  async getProgrammeBasketByEmail(email, request): Promise<any> {
    const requestId = request?.body?.requestId;
    try {
      await this.log(
        loggerEnum.Event.GET_PROGRAMME_BASKET_BY_EMAIL,
        { email },
        { email },
        'Get programme basket by email initiated',
        requestId,
        email,
        'Application',
        '',
      );
      const data = await this.dynamoDBService.getObject(
        process.env.APPHERO_COMPARE_PROGRAMME_BASKET_TABLE,
        {
          PK: email,
        },
      );
      const retrievedItem = data.Item;
      await this.log(
        loggerEnum.Event.GET_PROGRAMME_BASKET_BY_EMAIL,
        { email },
        { email },
        'Get programme basket by email completed',
        requestId,
        email,
        'Application',
        '',
        retrievedItem,
      );
      return retrievedItem;
    } catch (error) {
      await this.error(
        loggerEnum.Event.GET_PROGRAMME_BASKET_BY_EMAIL,
        { email },
        { email },
        error.message ? error.message : JSON.stringify(error),
        requestId,
        email,
        'Application',
        '',
      );
      throw error;
    }
  }
  async addProgrammeToBasket(programmeDetails, request?): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];
    try {
      this.log(
        loggerEnum.Event.INITIATED_ADD_PROGRAMME_TO_BASKET,
        programmeDetails,
        programmeDetails,
        'Add programme to basket initiated',
        requestId,
        '',
        'Application',
        '',
        null,
        usecase,
      );
      const PK = programmeDetails.email;
      await this.dynamoDBService.putObject(
        process.env.APPHERO_COMPARE_PROGRAMME_BASKET_TABLE,
        {
          Item: {
            PK: PK,
            ...programmeDetails,
          },
        },
      );
      this.log(
        loggerEnum.Event.COMPLETED_ADD_PROGRAMME_TO_BASKET,
        programmeDetails,
        programmeDetails,
        'Add programme to basket completed',
        requestId,
        '',
        'Application',
        '',
        null,
        usecase,
      );
      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        programmeDetails,
        programmeDetails,
        'Operation completed',
        requestId,
        '',
        'Application',
        '',
        null,
        usecase,
      );
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_ADD_PROGRAMME_TO_BASKET,
        programmeDetails,
        programmeDetails,
        error.message ? error.message : JSON.stringify(error),
        requestId,
        '',
        'Application',
        '',
        null,
        usecase,
      );
      console.log(error);
      throw error;
    }
  }
  convertToTargetFormat = (dates) => {
    const targetFormatDates = dates.map((dateString) => {
      const parts = dateString.split(' ');
      const month = parts[0];
      const year = parts[1];
      const monthNumeric =
        new Date(Date.parse(month + ' 1, ' + year)).getMonth() + 1;
      const formattedMonth =
        monthNumeric < 10 ? '0' + monthNumeric : monthNumeric;

      return year + '-' + formattedMonth;
    });

    return targetFormatDates;
  };

  async log(
    apiEvent,
    sourcePayload,
    destinationPayload,
    logMessage,
    requestId?,
    email?,
    entityKey?,
    entityKeyField?,
    response?,
    usecase?,
  ) {
    await this.loggerService.log(
      requestId || uuidv4(),
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      loggerEnum.UseCase[`${usecase}`],
      sourcePayload,
      destinationPayload,
      logMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }

  async error(
    apiEvent,
    sourcePayload,
    destinationPayload,
    errorMessage,
    requestId?,
    email?,
    entityKey?,
    entityKeyField?,
    response?,
    usecase?,
  ) {
    await this.loggerService.error(
      requestId || uuidv4(),
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      loggerEnum.UseCase[`${usecase}`],
      sourcePayload,
      destinationPayload,
      errorMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }
}
