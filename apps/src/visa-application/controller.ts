import {
  Body,
  Controller,
  Req,
  Post,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Request } from 'express';
import { VisaApplicationService } from './service';

@Controller('apphero')
export class VisaApplicationController {
  constructor(
    private readonly visaApplicationService: VisaApplicationService,
  ) {}

  @Post('visaapplication')
  async createVisaApplication(
    @Body() visaApplicationDetails: any,
    @Req() request: Request,
  ): Promise<any> {
    try {
      return await this.visaApplicationService.visaApplicationProcess(
        visaApplicationDetails,
        request,
      );
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to process visa application',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
