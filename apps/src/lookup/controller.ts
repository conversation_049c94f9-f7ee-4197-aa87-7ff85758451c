import {
  Controller,
  Get,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Param,
  Query,
} from '@nestjs/common';
import { LookUpService } from './service';

@Controller('')
export class LookUpController {
  constructor(private readonly lookUpService: LookUpService) {}
  @Get('apphero/programme/lookupdata')
  async filterData(): Promise<any> {
    return await this.lookUpService.getProgramLookUpData();
  }
  @Get('apphero/document/types/:brand')
  async getDocumentTypesByBrand(@Param('brand') brand: string): Promise<any> {
    try {
      const result = await this.lookUpService.getDocumentTypesByBrand(brand);
      return { totalSize: result.Count, records: result.Items };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('apphero/lookup/country')
  async getcountries(): Promise<any> {
    return await this.lookUpService.getCountries();
  }
  @Get('unauth/apphero/lookup')
  async getlookup(
    @Query('lookuptype') lookuptype: string,
    @Query('lookup') lookupvalue?: string,
  ): Promise<any> {
    return await this.lookUpService.getLookUp(lookuptype, lookupvalue);
  }

  @Post('unauth/apphero/logger')
  async createNewLog(@Body() body: any): Promise<any> {
    try {
      return await this.lookUpService.createNewLog(body);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get('unauth/apphero/supportedbrands')
  async getBrand(): Promise<any> {
    return await this.lookUpService.getAppheroSupportedBrand();
  }
}
