import { Injectable } from '@nestjs/common';
import { addMonths, format } from 'date-fns';
import { SalesforceService } from '../common/salesforce.service';
import { DynamoDBService } from '../common/dynamodb.service';
import { S3Service } from '../common/s3.service';
import { CloudWatchLoggerService, LoggerEnum } from '@gus-eip/loggers';
const loggerEnum = new LoggerEnum();
@Injectable()
export class LookUpService {
  cloudWatchLoggerService: CloudWatchLoggerService;

  constructor(
    private readonly dynamoDBService: DynamoDBService,
    private readonly s3Service: S3Service,
    private readonly salesforceService: SalesforceService,
  ) {
    this.cloudWatchLoggerService = new CloudWatchLoggerService(
      process.env.REGION,
      process.env.LOGGER_LOG_GROUP_NAME,
      process.env.TEAMS_WEBHOOK_URL,
      true,
    );
  }
  async getProgramLookUpData(): Promise<any> {
    try {
      const appheroSupportedBrand =
        await this.dynamoDBService.getAppheroSupportedBrand();
      const filterExpression = appheroSupportedBrand
        .map((_, index) => `#businessUnitFilter__c = :brand${index}`)
        .join(' OR ');
      const expressionAttributeValues = appheroSupportedBrand.reduce(
        (acc, brand, index) => {
          acc[`:brand${index}`] = brand;
          return acc;
        },
        {},
      );
      let allInstitutions = await this.dynamoDBService.queryObjects(
        await this.constructQuery(
          process.env.APPHERO_LOOKUP_TABLE,
          `BusinessUnit`,
          '#businessUnitFilter__c,#name',
          {
            '#businessUnitFilter__c': 'BusinessUnitFilter__c',
            '#name': 'Name',
          },
          filterExpression,
          expressionAttributeValues,
        ),
      );
      allInstitutions['Items'] = Array.from(
        new Set(
          allInstitutions.Items?.map(
            (institution) => institution.BusinessUnitFilter__c,
          ),
        ),
      )
        .map((brand) =>
          allInstitutions.Items?.find(
            (institution) => institution.BusinessUnitFilter__c === brand,
          ),
        )
        .sort((a, b) => a.Name.localeCompare(b.Name));

      const allLevels = await this.dynamoDBService.queryObjects(
        await this.constructQuery(
          process.env.APPHERO_LOOKUP_TABLE,
          `Level`,
          '#id,#name',
          {
            '#id': 'Id',
            '#name': 'Name',
          },
        ),
      );

      const allSubjects = await this.dynamoDBService.queryObjects(
        await this.constructQuery(
          process.env.APPHERO_LOOKUP_TABLE,
          `Vertical`,
          '#id,#name',
          {
            '#id': 'Id',
            '#name': 'Name',
          },
        ),
      );

      const intake = await this.Intake();
      const lookupData = {
        level: {
          filterType: 'checkbox',
          options: allLevels.Items?.map((level) => ({
            value: level.Name,
            name: level.Name,
          })).sort((a, b) => a.name.localeCompare(b.name)),
          defaultValues: [],
        },
        subject: {
          filterType: 'checkbox',
          options: allSubjects.Items?.map((subject) => ({
            value: subject.Name,
            name: subject.Name,
          })).sort((a, b) => a.name.localeCompare(b.name)),
          defaultValues: [],
        },
        intake: {
          filterType: 'select',
          options: intake.map((intake) => ({
            value: intake,
            name: intake,
          })),
          defaultValues: [],
        },
      };

      const params = {
        TableName: process.env.APPHERO_LOOKUP_TABLE,
        KeyConditionExpression: 'PK = :pkValue',
        ExpressionAttributeValues: {
          ':pkValue': 'Brand',
        },
      };
      const response = await this.dynamoDBService.queryObjects(params);
      const allInstitutionFromDb = response.Items;
      const brandToBrandFullNameMap: Record<string, string> = {};
      for (const entry of allInstitutionFromDb) {
        brandToBrandFullNameMap[entry.SK] = entry.BrandFullName;
      }
      const institution = {
        institution: {
          filterType: 'checkbox',
          options: allInstitutions.Items.map((institution) => ({
            value: institution.BusinessUnitFilter__c,
            name: brandToBrandFullNameMap[institution.BusinessUnitFilter__c],
          })),
          defaultValues: [],
        },
      };
      const lookupDataResponse = {
        ...institution,
        ...lookupData,
      };
      return lookupDataResponse;
    } catch (error) {
      throw error;
    }
  }
  async Intake(): Promise<any> {
    try {
      const currentDate = new Date();
      const intake: any[] = [];
      for (let i = 0; i <= 20; i++) {
        const intakeDate = addMonths(currentDate, i);
        intake.push(format(intakeDate, 'MMMM yyyy'));
      }
      return intake;
    } catch (error) {
      throw error;
    }
  }
  async getDocumentTypesByBrand(brand): Promise<any> {
    const queryRequest: AWS.DynamoDB.DocumentClient.QueryInput = {
      TableName: process.env.APPHERO_LOOKUP_TABLE,
      KeyConditionExpression: 'PK = :pkValue',
      FilterExpression:
        '#hideInFileUploader__c = :hideInFileUploader__c AND #validationName__c = :validationName__c',
      ExpressionAttributeNames: {
        '#hideInFileUploader__c': 'HideInFileUploader__c',
        '#validationName__c': 'ValidationName__c',
      },
      ExpressionAttributeValues: {
        ':pkValue': `DocumentType#${brand}`,
        ':hideInFileUploader__c': false,
        ':validationName__c': null,
      },
      ProjectionExpression: 'Id,DocumentType__c,Brand__c',
    };
    return await this.dynamoDBService.queryObjects(queryRequest);
  }
  async constructQuery(
    tableName,
    pk,
    projectionExpression,
    expressionAttributeNames,
    filterExpression?,
    expressionAttributeValues?,
  ): Promise<any> {
    const query = {
      TableName: tableName,
      KeyConditionExpression: 'PK = :pkValue',
      ExpressionAttributeValues: {
        ':pkValue': pk,
        ...expressionAttributeValues,
      },
      ProjectionExpression: projectionExpression,
      ExpressionAttributeNames: expressionAttributeNames,
    };

    if (filterExpression) {
      query['FilterExpression'] = filterExpression;
    }

    return query;
  }

  async getCountries(): Promise<any> {
    const countries = await this.dynamoDBService.queryObjects(
      await this.constructQuery(
        process.env.APPHERO_LOOKUP_TABLE,
        `Country`,
        '#label,#value',
        {
          '#value': 'Value',
          '#label': 'Label',
        },
      ),
    );
    return countries.Items.sort((a, b) => {
      if (a.Label < b.Label) {
        return -1;
      }
      if (a.Label > b.Label) {
        return 1;
      }
      return 0;
    });
  }
  async getLookUp(lookuptype: string, lookup): Promise<any> {
    return await this.dynamoDBService.getObject(
      process.env.APPHERO_LOOKUP_TABLE,
      {
        PK: lookuptype,
        SK: lookup,
      },
    );
  }

  async createNewLog(body: any): Promise<any> {
    if (
      !body ||
      !body.payload ||
      !body.response ||
      !body.hasOwnProperty('isErrorLog') ||
      !body.logStream ||
      !body.event ||
      !body.useCase
    ) {
      throw new Error('Missing required fields');
    }

    const { payload, response, isErrorLog, logStream } = body;

    try {
      if (isErrorLog) {
        await this.cloudWatchLoggerService.error(
          payload.email.toLowerCase(),
          new Date().toISOString(),
          loggerEnum.Component.APPHERO_FRONTEND,
          loggerEnum.Component.APPHERO_FRONTEND,
          loggerEnum.Component.APPHERO_BACKEND,
          body.event,
          body.useCase,
          payload,
          JSON.stringify(response),
          JSON.stringify(body.errorMessage) || JSON.stringify(response.message),
          'APPHERO',
          payload.email,
          logStream,
          'Email',
          payload.email,
        );
      } else {
        await this.cloudWatchLoggerService.log(
          payload.email.toLowerCase(),
          new Date().toISOString(),
          loggerEnum.Component.APPHERO_FRONTEND,
          loggerEnum.Component.APPHERO_FRONTEND,
          loggerEnum.Component.APPHERO_BACKEND,
          body.event,
          body.useCase,
          payload,
          JSON.stringify(response),
          JSON.stringify(body.logMessage) || JSON.stringify(response.message),
          'APPHERO',
          payload.email,
          logStream,
          'Email',
          payload.email,
        );
      }
    } catch (error) {
      throw error;
    }
  }

  async getAppheroSupportedBrand(): Promise<any> {
    return await this.dynamoDBService.getAppheroSupportedBrand();
  }
}
