import { forwardRef, Module } from '@nestjs/common';
import { OpportunityController } from './controller';
import { OpportunityService } from './service';
import { OptimizedOpportunityService } from './optimized-opportunity.service';
import { CommonModule } from '../common/module';
import { LookUpModule } from '../lookup/module';
import { NotificationModule } from '../notification/module';
import { LoggerModule } from '@gus-eip/loggers';
import { ConfigModule } from '@nestjs/config';
@Module({
  imports: [CommonModule, LookUpModule, forwardRef(() => NotificationModule)],
  controllers: [OpportunityController],
  providers: [
    OpportunityController,
    OpportunityService,
    OptimizedOpportunityService,
  ],
  exports: [
    OpportunityController,
    OpportunityService,
    OptimizedOpportunityService,
  ],
})
export class OpportunityModule {}
