import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { DirectSalesforceService } from '../common/direct-salesforce.service';
import { DynamoDBService } from '../common/dynamodb.service';
import { LoggerService } from '../common/cloudwatchService';
import { LoggerEnum } from '@gus-eip/loggers';
import { v4 as uuidv4 } from 'uuid';

const loggerEnum = new LoggerEnum();

@Injectable()
export class OptimizedOpportunityService {
  constructor(
    private readonly directSalesforceService: DirectSalesforceService,
    private readonly dynamoDBService: DynamoDBService,
    private readonly loggerService: LoggerService,
  ) {}

  /**
   * Optimized method to get opportunities by email using direct Salesforce calls
   * instead of middleware service calls
   */
  async getOpportunitiesByEmailOptimized(
    event: any,
    request?: any,
  ): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];

    try {
      if (!event.email) {
        throw new Error('email is required');
      }

      this.log(
        loggerEnum.Event.INITIATED_OPPORTUNITY_BY_EMAIL,
        event,
        event,
        'Get opportunities by email initiated (optimized)',
        requestId,
        event.email,
        'AccountEmail__c',
        event?.email,
        null,
        usecase,
      );

      const appheroSupportedBrand =
        await this.dynamoDBService.getAppheroSupportedBrand();
      const excludedStageName = 'Application';

      const filterExpression = `(${appheroSupportedBrand
        .map((_, index) => `#businessUnitFilter__c = :brand${index}`)
        .join(' OR ')}) AND #StageName <> :stageName`;

      const expressionAttributeNames = {
        '#businessUnitFilter__c': 'BusinessUnitFilter__c',
        '#StageName': 'StageName',
      };

      const expressionAttributeValues = appheroSupportedBrand.reduce(
        (acc, brand, index) => {
          acc[`:brand${index}`] = brand;
          return acc;
        },
        { ':stageName': excludedStageName },
      );

      const queryParams: any = {
        TableName: process.env.APPHERO_SF_OPPORTUNITY_TABLE,
        KeyConditionExpression: 'PK = :pk',
        FilterExpression: filterExpression,
        ExpressionAttributeValues: {
          ':pk': event.email.toLowerCase(),
          ...expressionAttributeValues,
        },
        ExpressionAttributeNames: expressionAttributeNames,
      };

      const opportunities = await this.dynamoDBService.queryObjects(
        queryParams,
      );

      if (opportunities.Items.length === 0) {
        this.log(
          loggerEnum.Event.INITIATED_OPPORTUNITY_FROM_SALESFORCE,
          event,
          event,
          'Get opportunities from SF initiated (optimized)',
          requestId,
          event.email,
          'AccountEmail__c',
          event?.email,
          null,
          usecase,
        );

        // Use direct Salesforce API call instead of middleware
        const directOpportunities =
          await this.getOpportunitiesByEmailFromSfOptimized(event);
        opportunities.Items.push(...directOpportunities);

        this.log(
          loggerEnum.Event.COMPLETED_OPPORTUNITY_FROM_SALESFORCE,
          event,
          event,
          'Get opportunities from SF completed (optimized)',
          requestId,
          event.email,
          'AccountEmail__c',
          event?.email,
          '',
          usecase,
        );
      }

      this.log(
        loggerEnum.Event.COMPLETED_OPPORTUNITY_BY_EMAIL,
        event,
        event,
        'Get opportunities by email completed (optimized)',
        requestId,
        event.email,
        'AccountEmail__c',
        event?.email,
        opportunities.Items,
        usecase,
      );

      return { response: opportunities.Items };
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_OPPORTUNITY_BY_EMAIL,
        event,
        event,
        error.message ? error.message : JSON.stringify(error),
        requestId,
        event?.email,
        'AccountEmail__c',
        event?.email,
        null,
        usecase,
      );
      throw error;
    }
  }

  /**
   * Optimized method to get opportunities by email from Salesforce using direct API calls
   */
  async getOpportunitiesByEmailFromSfOptimized(event: any): Promise<any[]> {
    try {
      console.log('Fetching opportunities from Salesforce using direct API...');
      const response =
        await this.directSalesforceService.getOpportunitiesByEmail(event.email);

      if (!response.records || response.records.length === 0) {
        console.log(process.env.TEAMS_WEBHOOK_URL);
        throw new NotFoundException(
          `No Opportunities found for this email - ${event.email}`,
        );
      }

      const mappedOpportunities = response.records.map(
        (opportunityDetails) => ({
          PK: opportunityDetails?.Account?.PersonEmail,
          SK: opportunityDetails.Id,
          createdAt: new Date().toISOString(),
          ...opportunityDetails,
        }),
      );

      // Save to DynamoDB for future use
      if (mappedOpportunities.length > 0) {
        await this.dynamoDBService.uploadBulkDataToDynamoDB(
          mappedOpportunities,
          process.env.APPHERO_SF_OPPORTUNITY_TABLE,
        );
      }

      console.log(
        `Found ${mappedOpportunities.length} opportunities using direct SF API`,
      );
      return mappedOpportunities;
    } catch (error) {
      console.log(
        'Error fetching opportunities from Salesforce using direct API:',
        error,
      );
      throw error;
    }
  }

  /**
   * Log method for tracking optimized operations
   */
  async log(
    apiEvent: string,
    sourcePayload: any,
    destinationPayload: any,
    logMessage: string,
    requestId?: string,
    email?: string,
    entityKey?: string,
    entityKeyField?: string,
    response?: any,
    usecase?: string,
  ) {
    await this.loggerService.log(
      requestId || uuidv4(),
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      '',
      sourcePayload,
      destinationPayload,
      logMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }

  /**
   * Error logging method for optimized operations
   */
  async error(
    apiEvent: string,
    sourcePayload: any,
    destinationPayload: any,
    errorMessage: string,
    requestId?: string,
    email?: string,
    entityKey?: string,
    entityKeyField?: string,
    response?: any,
    usecase?: string,
  ) {
    await this.loggerService.error(
      requestId || uuidv4(),
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      '',
      sourcePayload,
      destinationPayload,
      errorMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }
}
