import {
  Controller,
  Get,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Param,
  Headers,
  ForbiddenException,
  Query,
  BadRequestException,
  Req,
} from '@nestjs/common';
import { OpportunityService } from './service';
import { AuthService } from 'apps/src/common/auth.service';
import { Request } from 'express';
@Controller('apphero')
export class OpportunityController {
  constructor(
    private readonly opportunityService: OpportunityService,
    private readonly authService: AuthService,
  ) {}

  @Get('/opportunities/:id/:email')
  async opportunityByIdAndEmail(
    @Param('id') id: string,
    @Param('email') email: string,
    @Req() request: Request,
    @Headers('x-id-token') token?: string,
  ): Promise<any> {
    const opportunity =
      await this.opportunityService.getOpportunityByIdAndEmail(
        id,
        email.toLowerCase(),
        request,
      );
    if (token) {
      const userDetails = await this.authService.extractUserDetailsFromToken(
        token,
      );
      if (
        userDetails.email != opportunity?.['response'][0]?.Account?.PersonEmail
      ) {
        throw new ForbiddenException('Forbidden');
      }
    }
    return opportunity;
  }

  @Get('/taskbyopportunityid/:opportunityid')
  async getReviewCenterTaskByOpportunityId(
    @Param('opportunityid') opportunityId: string,
    @Req() request: Request,
  ): Promise<any> {
    const taskDetails: TaskDetails = {
      ActionRequiredItems: { Items: [] },
      outstandingActionRequiredCount: 0,
      completedActionRequiredCount: 0,
    };

    const actionRequiredItems =
      await this.opportunityService.getReviewCenterTaskByOpportunityId(
        opportunityId,
        request,
      );

    if (actionRequiredItems && actionRequiredItems.Items) {
      taskDetails.ActionRequiredItems = actionRequiredItems;
      taskDetails.outstandingActionRequiredCount =
        taskDetails.ActionRequiredItems?.Items?.filter(
          (item) => item.Status === 'Open',
        ).length;
      taskDetails.completedActionRequiredCount =
        taskDetails.ActionRequiredItems?.Items?.filter(
          (item) => item.Status === 'Completed',
        ).length;
    }

    return taskDetails;
  }

  @Post('/opportunitiesbyemail')
  async opportunitiesByEmail(
    @Body() event,
    @Req() request: Request,
  ): Promise<any> {
    return await this.opportunityService.getOpportunitiesByEmail(
      event,
      request,
    );
  }

  @Get('/opportunityfilesbyopportunityid/:opportunityId')
  async opportunityFiles(
    @Param('opportunityId') opportunityId: string,
    @Req() request: Request,
  ): Promise<any> {
    return await this.opportunityService.getOpportunityFiles(
      opportunityId,
      request,
    );
  }

  @Post('/oppotunityfile/signedurl')
  async uploadFile(@Body() event, @Req() request: Request) {
    try {
      return await this.opportunityService.getOpportunityFilesS3SignedUrl(
        event,
        request,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('/opportunityfiles/closetask')
  async closeDocumentSubmissionTask(@Body() event, @Req() request: Request) {
    try {
      if (
        event &&
        event.files &&
        Array.isArray(event.files) &&
        event.files.length > 0 &&
        event.taskId &&
        event.opportunityId &&
        event.BusinessUnitFilter__c &&
        event.applicationFormId &&
        event.email &&
        event != undefined &&
        event.email != undefined &&
        event.taskId != undefined &&
        event.opportunityId != undefined &&
        event.BusinessUnitFilter__c != undefined &&
        event.applicationFormId != undefined
      ) {
        // Validate each file in the array
        const isValidFiles = event.files.every(
          (file) =>
            file &&
            file.objectKey &&
            file.contentType &&
            file.documentType &&
            file.fileName &&
            file.objectKey != undefined &&
            file.contentType != undefined &&
            file.documentType != undefined &&
            file.fileName != undefined,
        );

        if (!isValidFiles) {
          throw new BadRequestException(
            'Missing required fields in one or more files. Required: objectKey, contentType, documentType, fileName',
          );
        }

        return await this.opportunityService.closeDocumentSubmissionTask(
          event,
          request,
        );
      } else {
        throw new BadRequestException(
          'Missing required fields or invalid files array',
        );
      }
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('/opportunityfiles/generatesignedurl')
  async generateSignedUrl(
    @Query('objectKey') objectKey: string,
    @Query('toDownload') toDownload: boolean = false,
    @Req() request: Request,
  ) {
    try {
      return await this.opportunityService.generateSignedUrl(
        objectKey,
        toDownload,
        request,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Post('/tasksbyemail')
  async getTaskByEmail(@Body() event) {
    try {
      if (
        (event && event.email, event != undefined && event.email != undefined)
      ) {
        return await this.opportunityService.getTasksByEmail(event);
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('/uploadstudentdocument/getsignedurl')
  async uploadDocuments(
    @Body() docDetails: any,
    @Req() request: Request,
  ): Promise<string> {
    try {
      if (
        docDetails.applicationFormId &&
        docDetails.documentType &&
        docDetails.fileName &&
        docDetails.contentType &&
        docDetails.BusinessUnitFilter__c &&
        docDetails.applicationFormId != undefined &&
        docDetails.documentType != undefined &&
        docDetails.fileName != undefined &&
        docDetails.contentType != undefined &&
        docDetails.BusinessUnitFilter__c !== undefined
      ) {
        return await this.opportunityService.getSignedUrlToUploadDoc(
          docDetails,
          request,
        );
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      console.error('Error on file upload:', error);
      throw error;
    }
  }

  @Post('/deletedocument')
  async deleteDocument(
    @Body() docDetails: any,
    @Req() request: Request,
  ): Promise<string> {
    try {
      if (
        docDetails.objectKey &&
        docDetails.BusinessUnitFilter__c &&
        docDetails.BusinessUnitFilter__c !== undefined &&
        docDetails.objectKey !== undefined
      ) {
        return await this.opportunityService.deleteFile(docDetails, request);
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      console.error('Error on file upload:', error);
      throw error;
    }
  }
}

type TaskDetails = {
  ActionRequiredItems: any;
  outstandingActionRequiredCount?: number;
  completedActionRequiredCount?: number;
};
