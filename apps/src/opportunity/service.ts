import {
  forwardRef,
  HttpStatus,
  HttpException,
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { SalesforceService } from '../common/salesforce.service';
import { DynamoDBService } from '../common/dynamodb.service';
import { S3Service } from '../common/s3.service';
import { v4 as uuidv4 } from 'uuid';
import { LookUpService } from '../lookup/service';
import { NotificationContentBuilder } from '../notification/notificationContentBuilder/notificationContentBuilder';
import { OptimizedOpportunityService } from './optimized-opportunity.service';
import { LoggerService } from '../common/cloudwatchService';
import { LoggerEnum } from '@gus-eip/loggers';
const loggerEnum = new LoggerEnum();
@Injectable()
export class OpportunityService {
  constructor(
    private readonly dynamoDBService: DynamoDBService,
    private readonly salesforceService: SalesforceService,
    private readonly s3Service: S3Service,
    private readonly lookUpService: LookUpService,
    private readonly loggerService: LoggerService,
    private readonly optimizedOpportunityService: OptimizedOpportunityService,
    @Inject(forwardRef(() => NotificationContentBuilder))
    private readonly notificationContentBuilder: NotificationContentBuilder,
  ) {}

  /**
   * Builds optimized filter expression that combines brand filtering with draft stage filtering
   * @param supportedBrands Array of all supported brand names
   * @param draftAllowedBrands Array of brands that allow draft applications
   * @returns Object containing filterExpression, expressionAttributeNames, and expressionAttributeValues
   */
  private buildBrandFilterExpression(
    supportedBrands: string[],
    draftAllowedBrands: string[],
  ) {
    // Create brand filter expressions
    const allBrandsFilter = supportedBrands
      .map((_, index) => `#businessUnitFilter__c = :brand${index}`)
      .join(' OR ');

    const draftBrandsFilter = draftAllowedBrands
      .map((_, index) => `#businessUnitFilter__c = :draftBrand${index}`)
      .join(' OR ');

    // Combine filters: (all brands AND NOT draft stage) OR (draft allowed brands AND draft stage)
    const filterExpression = `((${allBrandsFilter}) AND #stageName <> :draftStage) OR ((${draftBrandsFilter}) AND #stageName = :draftStage)`;

    const expressionAttributeNames = {
      '#businessUnitFilter__c': 'BusinessUnitFilter__c',
      '#stageName': 'StageName',
    };

    // Build expression attribute values
    const expressionAttributeValues = supportedBrands.reduce(
      (acc, brand, index) => {
        acc[`:brand${index}`] = brand;
        return acc;
      },
      {} as Record<string, any>,
    );

    // Add draft allowed brands
    draftAllowedBrands.forEach((brand, index) => {
      expressionAttributeValues[`:draftBrand${index}`] = brand;
    });

    // Add draft stage value
    expressionAttributeValues[':draftStage'] = 'Application';

    return {
      filterExpression,
      expressionAttributeNames,
      expressionAttributeValues,
    };
  }

  async getBrandDetails(brand): Promise<any> {
    return await this.dynamoDBService.getObject(
      process.env.APPHERO_LOOKUP_TABLE,
      {
        PK: 'Brand',
        SK: brand,
      },
    );
  }
  daysToWeeks(days: number): number {
    const daysInWeek = 7; // Number of days in a week
    return days / daysInWeek;
  }

  async getOpportunityByIdAndEmail(
    id,
    email?: string,
    request?,
    fromDb = true,
  ) {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];
    try {
      this.log(
        loggerEnum.Event.INITIATED_OPPORTUNITY_BY_ID_AND_EMAIL,
        { id, email },
        { id, email },
        'Get opportunities by id and email initiated',
        requestId,
        email,
        'AccountEmail__c',
        email,
        null,
        usecase,
      );

      let opportunityDetails;
      if (fromDb) {
        const opportunity = await this.dynamoDBService.getObject(
          process.env.APPHERO_SF_OPPORTUNITY_TABLE,
          {
            PK: email,
            SK: id,
          },
        );
        opportunityDetails = opportunity.Item;
      }
      console.log('Opp -->', opportunityDetails);
      if (!opportunityDetails) {
        console.log('Fetch from salesforce ...');
        const response = await this.salesforceService.fetchData(
          `gus/getOpportunitiesById/${id}`,
        );
        opportunityDetails = response[0];
        if (!opportunityDetails) {
          throw new NotFoundException(
            `No opportunity found for this Id - ${id}`,
          );
        } else {
          console.log('Create in db ...');
          await this.createOpportunityInDb(id, opportunityDetails);
        }
      }
      if (opportunityDetails?.BusinessUnitFilter__c) {
        let brandDetails;
        try {
          brandDetails = await this.getBrandDetails(
            opportunityDetails?.BusinessUnitFilter__c,
          );
        } catch (error) {
          console.log(error);
        }
        opportunityDetails['brandFullName'] = brandDetails?.Item?.BrandFullName;
      }

      opportunityDetails.isDirectSales =
        opportunityDetails?.ApplicationSource__c === 'Agent Portal' ||
        opportunityDetails?.ApplicationSource__c === 'Portal uLink'
          ? false
          : true;
      if (opportunityDetails?.isDirectSales === false) {
        opportunityDetails = await this.updateAgentDetails(opportunityDetails);
      }
      opportunityDetails.status = 'Active';
      opportunityDetails['ApplicationStatus'] =
        this.applicationStatusConfig?.[
          opportunityDetails['Student_Placement_Status__c']
        ] || this.applicationStatusConfig?.[opportunityDetails['StageName']];
      this.mapIntakeDate(opportunityDetails);
      if (opportunityDetails?.['ApplicationStatus']?.['status'] === 'Draft') {
        opportunityDetails['DraftMessage'] =
          await this.getDraftMessageByApplicationSource(
            opportunityDetails?.ApplicationSource__c,
          );
      }
      await this.updateDuration(opportunityDetails);

      console.log('Opp', opportunityDetails);
      this.log(
        loggerEnum.Event.COMPLETED_OPPORTUNITY_BY_ID_AND_EMAIL,
        { id, email },
        { id, email },
        'Get opportunities by id and email completed',
        requestId,
        email,
        'AccountEmail__c',
        email,
        [opportunityDetails],
        usecase,
      );
      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        { id, email },
        { id, email },
        'Operation completed',
        requestId,
        email,
        'AccountEmail__c',
        email,
        [opportunityDetails],
        usecase,
      );
      return { response: [opportunityDetails] };
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_OPPORTUNITY_BY_ID_AND_EMAIL,
        { id, email },
        { id, email },
        error.message ? error.message : JSON.stringify(error),
        email,
        requestId,
        'AccountEmail__c',
        email,
      );

      throw error;
    }
  }

  async createOpportunityInDb(
    opportunityId: string,
    opportunityDetails?: any,
  ): Promise<any> {
    try {
      if (!opportunityDetails) {
        opportunityDetails = await this.getOpportunityByIdAndEmail(
          opportunityId,
          undefined,
          false,
        );
      }

      console.log('opp details', opportunityDetails);
      const params = {
        Item: {
          PK: opportunityDetails?.Account?.PersonEmail,
          SK: opportunityDetails?.Id,
          createdAt: new Date().toISOString(),
          ...opportunityDetails,
        },
      };
      await this.dynamoDBService.putObject(
        process.env.APPHERO_SF_OPPORTUNITY_TABLE,
        params,
      );
      await this.updateAgentDetails(opportunityDetails);
      return opportunityDetails?.response;
    } catch (error) {
      throw error;
    }
  }
  async updateAgentDetails(opportunityDetails) {
    try {
      const params = {
        TableName: process.env.APPHERO_APPLICATION_TABLE,
        KeyConditionExpression: 'PK = :pkValue',
        ExpressionAttributeValues: {
          ':pkValue': opportunityDetails.Id,
        },
      };

      let applicationDetails = await this.dynamoDBService.queryObjects(params);
      if (applicationDetails.Count === 0) {
        applicationDetails = await this.getApplicationFromSf(
          opportunityDetails.Id,
        );

        if (
          applicationDetails &&
          applicationDetails['PK'] &&
          applicationDetails['SK']
        ) {
          await this.dynamoDBService.putObject(
            process.env.APPHERO_APPLICATION_TABLE,
            {
              Item: applicationDetails,
            },
          );
          opportunityDetails.Agent_Contact__r =
            applicationDetails['Agent_Contact__r'];
        }
      } else if (applicationDetails?.Items) {
        opportunityDetails.Agent_Contact__r =
          applicationDetails.Items[0].Agent_Contact__r;
      }
      return opportunityDetails;
    } catch (error) {
      throw error;
    }
  }
  async getReviewCenterTaskByOpportunityId(id, request?) {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];
    try {
      this.log(
        loggerEnum.Event.INITIATED_GET_TASK_BY_OPPORTUNITY_ID_FROM_DB,
        { id },
        { id },
        'Get review center task by opportunityId from db initiated',
        requestId,
        '',
        'opportunityId',
        id,
        null,
        usecase,
      );

      const params = {
        TableName: process.env.APPHERO_TASK_TABLE,
        KeyConditionExpression: 'PK = :pkValue',
        ExpressionAttributeValues: {
          ':pkValue': id,
          ':subject1': 'Review Center Comments',
          ':subject2': 'Review Center Comment',
          ':subject3': 'Smart Apply Tasks',
          ':subject4': 'Institution Comments',
          ':subject5': 'UCW FOLLOWUP TASK',
          ':isDuplicateTask': false,
          ':createdDateValue': process.env.GET_TASK_FROM_DATE,
        },
        ExpressionAttributeNames: {
          '#duplicateTask': 'Duplicate_Task__c',
          '#createdDate': 'CreatedDate',
        },
        FilterExpression:
          '(contains(Subject, :subject1) OR contains(Subject, :subject2) OR contains(Subject, :subject3) OR contains(Subject, :subject4) OR contains(Subject, :subject5)) AND #duplicateTask = :isDuplicateTask AND #createdDate >= :createdDateValue',
      };

      // Perform the DynamoDB query
      const tasksFromDynamoDb = await this.dynamoDBService.queryObjects(params);

      const generateComment = (comment: string, relatedField: boolean) => {
        const [documentTypeWithInstitution, taskComment] = comment
          .split('|')
          .map((str) => str.trim());

        return relatedField
          ? `${documentTypeWithInstitution} | ${taskComment}`
          : taskComment;
      };

      // Helper function to parse descriptions with optional chaining for items
      const parseDescription = (items) =>
        items.map((item) => {
          const {
            Document_Type__c,
            Related_Record_Name__c,
            Related_Record_Id__c,
          } = item;
          if (Document_Type__c && item?.Description?.includes('|')) {
            return {
              ...item,
              documentType: Document_Type__c,
              comment: generateComment(
                item.Description,
                !!(Related_Record_Id__c && Related_Record_Name__c),
              ),
              relatedField: Related_Record_Name__c,
              relatedFieldId: Related_Record_Id__c,
              isDocumentUploaded: !!item.isDocumentUploaded,
            };
          } else if (item?.Description?.includes('|')) {
            const [documentType, , , relatedField, relatedFieldId] =
              item.Description.split('|').map((str) => str.trim());
            return {
              ...item,
              documentType,
              comment: generateComment(
                item.Description,
                !!(relatedField && relatedFieldId),
              ),
              isDocumentUploaded: !!item.isDocumentUploaded,
              relatedField,
              relatedFieldId,
            };
          }
          return {
            ...item,
            documentType: null,
            comment: item.Description,
            isDocumentUploaded: !!item.isDocumentUploaded,
          };
        });

      if (tasksFromDynamoDb?.Count > 0) {
        tasksFromDynamoDb.Items = parseDescription(tasksFromDynamoDb.Items);
        this.log(
          loggerEnum.Event.COMPLETED_GET_TASK_BY_OPPORTUNITY_ID_FROM_DB,
          { id },
          params,
          'Get review center task by opportunityId from database',
          requestId,
          '',
          'opportunityId',
          id,
          tasksFromDynamoDb,
          usecase,
        );

        this.log(
          loggerEnum.Event.OPERATION_COMPLETED,
          { id },
          { id },
          'Operation completed',
          requestId,
          '',
          'opportunityId',
          id,
          tasksFromDynamoDb,
          usecase,
        );

        return tasksFromDynamoDb;
      }

      this.log(
        loggerEnum.Event.INITIATED_GET_TASK_BY_OPPORTUNITY_ID_FROM_SALESFORCE,
        { id },
        { id },
        'Get review center task by opportunityId from SF initiated',
        requestId,
        '',
        'opportunityId',
        id,
        tasksFromDynamoDb,
        usecase,
      );
      // Retrieve tasks from Salesforce if no DynamoDB items are found
      const admissionCommentsPromise = await this.getTaskFromSf(id);
      const admissionComments = await admissionCommentsPromise;

      if (admissionComments?.length > 0) {
        this.dynamoDBService.uploadBulkDataToDynamoDB(
          admissionComments,
          process.env.APPHERO_TASK_TABLE,
        );
      }

      const parsedAdmissionComments = {
        Items: admissionComments ? parseDescription(admissionComments) : [],
      };

      this.log(
        loggerEnum.Event.COMPLETED_GET_TASK_BY_OPPORTUNITY_ID_FROM_SALESFORCE,
        { id },
        { id },
        'Get review center task by opportunityId from SF completed',
        requestId,
        '',
        'opportunityId',
        id,
        parsedAdmissionComments,
        usecase,
      );

      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        { id },
        { id },
        'Operation completed',
        requestId,
        '',
        'opportunityId',
        id,
        parsedAdmissionComments,
        usecase,
      );

      return parsedAdmissionComments;
    } catch (error) {
      console.error('Error in getReviewCenterTaskByOpportunityId:', error);
      this.error(
        loggerEnum.Event.FAILED_GET_TASK_BY_OPPORTUNITY_ID,
        { id },
        { id },
        error.message ? error.message : JSON.stringify(error),
        requestId,
        '',
        'opportunityId',
        id,
        null,
        usecase,
      );
      throw error;
    }
  }

  async getTaskFromSf(id: any) {
    const taskFromSalesForce = await this.salesforceService.fetchData(
      `gus/task/${id}`,
    );

    const mappedResponse = taskFromSalesForce.map(
      ({ attributes, ...taskDetails }) => ({
        PK: taskDetails.WhatId,
        SK: taskDetails.Id,
        ...taskDetails,
        isDocumentUploaded: taskDetails.Status === 'Open' ? false : true,
        createdAt: new Date().toISOString(),
      }),
    );

    return mappedResponse;
  }
  async getApplicationFromSf(id: any) {
    let applicationFromSalesForce = await this.salesforceService.fetchData(
      `gus/getapplicationbyopportunity/${id}`,
    );
    applicationFromSalesForce = applicationFromSalesForce?.[0];
    const mappedResponse = {
      PK: applicationFromSalesForce?.Opportunity_B2C__c,
      SK: applicationFromSalesForce?.Id,
      ...applicationFromSalesForce,
      createdAt: new Date().toISOString(),
    };

    return mappedResponse;
  }

  async getOpportunitiesByEmail(event, request?): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];

    const draftAllowedBrands =
      await this.dynamoDBService.getAppheroDraftSupportedBrand();

    try {
      if (!event.email) {
        throw new Error('email is required');
      }
      this.log(
        loggerEnum.Event.INITIATED_OPPORTUNITY_BY_EMAIL,
        event,
        event,
        'Get opportunities by email initiated',
        requestId,
        event.email,
        'AccountEmail__c',
        event?.email,
        null,
        usecase,
      );

      const appheroSupportedBrand =
        await this.dynamoDBService.getAppheroSupportedBrand();

      console.log('appheroSupportedBrand', appheroSupportedBrand);

      const {
        filterExpression,
        expressionAttributeNames,
        expressionAttributeValues,
      } = this.buildBrandFilterExpression(
        appheroSupportedBrand,
        draftAllowedBrands,
      );

      const queryParams: any = {
        TableName: process.env.APPHERO_SF_OPPORTUNITY_TABLE,
        KeyConditionExpression: 'PK = :pk',
        FilterExpression: filterExpression,
        ExpressionAttributeValues: {
          ':pk': event.email.toLowerCase(),
          ...expressionAttributeValues,
        },
        ExpressionAttributeNames: expressionAttributeNames,
      };
      const opportunities = await this.dynamoDBService.queryObjects(
        queryParams,
      );

      if (opportunities.Items.length === 0) {
        this.log(
          loggerEnum.Event.INITIATED_OPPORTUNITY_FROM_SALESFORCE,
          event,
          event,
          'Get opportunities from SF initiated',
          requestId,
          event.email,
          'AccountEmail__c',
          event?.email,
          null,
          usecase,
        );
        // Use optimized direct Salesforce calls instead of middleware
        opportunities.Items.push(
          ...(await this.optimizedOpportunityService.getOpportunitiesByEmailFromSfOptimized(
            event,
          )),
        );
        this.log(
          loggerEnum.Event.COMPLETED_OPPORTUNITY_FROM_SALESFORCE,
          event,
          event,
          'Get opportunities from SF completed',
          requestId,
          event.email,
          'AccountEmail__c',
          event?.email,
          '',
          usecase,
        );
      }

      if (opportunities.Items.length > 0) {
        // Split the opportunities array into two parts only if there are more than one
        const midpoint = Math.ceil(opportunities.Items.length / 2);

        // Define a function to process a batch of opportunities
        const processOpportunitiesBatch = async (
          opportunitiesBatch,
          batchName,
        ) => {
          console.log(`${batchName} started at: ${new Date().toISOString()}`);

          const results = await Promise.all(
            opportunitiesBatch.map(async (opportunity) => {
              opportunity['ApplicationStatus'] =
                this.applicationStatusConfig?.[
                  opportunity['Student_Placement_Status__c']
                ] || this.applicationStatusConfig?.[opportunity['StageName']];

              this.mapIntakeDate(opportunity);
              if (opportunity.OpportunityLineItems?.records?.[0]?.Location__c) {
                const location =
                  opportunity.OpportunityLineItems.records[0].Location__c;
                opportunity['location'] = location;
                await this.updateDuration(opportunity);
              }

              return opportunity;
            }),
          );

          console.log(`${batchName} finished at: ${new Date().toISOString()}`);
          return results;
        };

        const allResults =
          opportunities.Items.length === 1
            ? await processOpportunitiesBatch(
                opportunities.Items,
                'Single Batch',
              )
            : await Promise.all([
                processOpportunitiesBatch(
                  opportunities.Items.slice(0, midpoint),
                  'First Batch',
                ),
                processOpportunitiesBatch(
                  opportunities.Items.slice(midpoint),
                  'Second Batch',
                ),
              ]);

        const processedOpportunities = allResults.flat();

        processedOpportunities.sort(this.sortList);

        this.log(
          loggerEnum.Event.COMPLETED_OPPORTUNITY_BY_EMAIL,
          event,
          event,
          'Get Opportunities by email completed',
          requestId,
          event.email,
          'AccountEmail__c',
          event?.email,
          processedOpportunities,
          loggerEnum.UseCase[`${usecase}`],
        );

        this.log(
          loggerEnum.Event.OPERATION_COMPLETED,
          event,
          event,
          'Operation completed',
          requestId,
          event.email,
          'AccountEmail__c',
          event?.email,
          processedOpportunities,
          loggerEnum.UseCase[`${usecase}`],
        );

        return { response: processedOpportunities };
      }
      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        event,
        event,
        'Operation completed',
        requestId,
        event.email,
        'AccountEmail__c',
        event?.email,
        [],
        usecase,
      );

      return { response: [] };
    } catch (error) {
      console.log('Error ->', error);
      this.error(
        loggerEnum.Event.FAILED_OPPORTUNITY_BY_EMAIL,
        event,
        event,
        error.message ? error.message : JSON.stringify(error),
        requestId,
        event.email,
        'AccountEmail__c',
        event?.email,
        null,
        usecase,
      );
      throw error;
    }
  }

  async updateDuration(opportunity: any): Promise<any> {
    const product = opportunity?.OpportunityLineItems?.records[0]?.Product2;
    const brand = opportunity?.BusinessUnitFilter__c;

    if (
      brand === 'IBAT' &&
      product &&
      !product.Duration__c &&
      product.Campus_Days__c
    ) {
      const durationInWeeks = this.daysToWeeks(product.Campus_Days__c);
      product.Duration__c = `${durationInWeeks} ${
        durationInWeeks === 1 ? 'Week' : 'Weeks'
      }`;
    } else if (product && product.Duration__c) {
      product.Duration__c = `${product.Duration__c} ${
        product.Duration__c === 1 ? 'Month' : 'Months'
      }`;
    }

    return opportunity;
  }

  sortList(a: { CreatedDate: string }, b: { CreatedDate: string }): number {
    return (
      new Date(b.CreatedDate).getTime() - new Date(a.CreatedDate).getTime()
    );
  }

  async getOpportunitiesByEmailFromSf(event) {
    const opportunities = await this.salesforceService.postData(
      'gus/getOpportunitiesByEmail',
      event,
    );
    if (!opportunities.length) {
      console.log(process.env.TEAMS_WEBHOOK_URL);
      throw new NotFoundException(
        `No Opportunities found for this email - ${event.email}`,
      );
    }
    if (opportunities === 'Unauthorized') {
      throw new UnauthorizedException('Unauthorized');
    }

    const mappedOpportunities = opportunities.map((opportunityDetails) => ({
      PK: opportunityDetails?.Account?.PersonEmail,
      SK: opportunityDetails.Id,
      createdAt: new Date().toISOString(),
      ...opportunityDetails,
    }));
    this.dynamoDBService.uploadBulkDataToDynamoDB(
      mappedOpportunities,
      process.env.APPHERO_SF_OPPORTUNITY_TABLE,
    );
    return mappedOpportunities;
  }
  async getDraftMessageByApplicationSource(
    applicationSource: string,
  ): Promise<any> {
    const draftMessagesBySource = {
      'Agent Portal':
        'Note: Please speak to your Agent to finalize your application',
      OAP: 'Note: Please visit the institution’s online application portal to finalize your application',
      'Invite to Apply':
        'Note: Please visit the institution’s online application portal to finalize your application',
      'Portal uLink':
        'Note: Please visit the application link provided by your Agent to finalize your application',
    };
    return draftMessagesBySource[applicationSource];
  }
  applicationStatusConfig = {
    Application: {
      category: 'Drafts',
      status: 'Drafts',
    },
    'Documents Stage': {
      category: 'Admission Review',
      status: 'Admission Review',
    },
    'Admissions Stage': {
      category: 'Admission Review',
      status: 'Admission Review',
    },
    Offer: {
      category: 'Offers',
      status: 'Offer',
    },
    Payment: {
      category: 'Deposit',
      status: 'Deposit',
    },
    Acceptance: {
      category: 'Deposit',
      status: 'Deposit',
    },
    Visa: {
      category: 'Deposit',
      status: 'Deposit',
    },
    'Closed Won': {
      category: 'Enrolment',
      status: 'Enrolment',
    },
    'Closed Lost': {
      category: 'Withdrawn',
      status: 'Withdrawn',
    },
    Enrolled: {
      category: 'Enrolment',
      status: 'Enrolment',
    },
  };

  async getOpportunityFiles(opportunityId, request?): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];
    try {
      // create an db call before calling sf api
      const queryParams = {
        TableName: process.env.APPHERO_SF_OPPORTUNITYFILE_TABLE,
        KeyConditionExpression: 'PK = :pk',
        ExpressionAttributeValues: { ':pk': opportunityId },
      };
      this.log(
        loggerEnum.Event.INITIATED_OPPORTUNITY_FILES,
        { opportunityId },
        queryParams,
        'Get opportunity files initiated',
        requestId,
        '',
        'opportunityId',
        opportunityId,
        null,
        usecase,
      );
      let opportunityFiles;
      const opportunityFileInfo = await this.dynamoDBService.queryObjects(
        queryParams,
      );

      if (opportunityFileInfo.Items.length != 0) {
        opportunityFiles = opportunityFileInfo?.Items;
      } else {
        opportunityFiles = await this.salesforceService.fetchData(
          `gus/getopportunityfiles/${opportunityId}`,
        );
        opportunityFiles = opportunityFiles.response;
        opportunityFiles.forEach(async (opportunityFile) => {
          const createRecordInDatabase = await this.dynamoDBService.putObject(
            process.env.APPHERO_SF_OPPORTUNITYFILE_TABLE,
            {
              Item: {
                PK: opportunityId,
                SK: opportunityFile?.Id,
                ...opportunityFile,
                createdAt: new Date().toISOString(),
              },
            },
          );
        });
      }
      for (const file of opportunityFiles) {
        if (
          (file?.LetterType__c !== null &&
            file?.LetterType__c !== undefined &&
            file?.Opportunity__r?.BusinessUnitFilter__c !== 'UEG') ||
          (file?.Opportunity__r?.BusinessUnitFilter__c === 'UEG' &&
            file?.DocumentSource__c === 'CampusNet')
        ) {
          if (file.DocumentType__c === 'Offer letter') {
            file['OfferLetter'] = true;
          }
          file.AdmissionLetter = true;
        }
      }
      if (opportunityFiles.length === 0) {
        this.log(
          loggerEnum.Event.NOT_FOUND_OPPORTUNITY_FILES,
          { opportunityId },
          { opportunityId },
          `No Documents found for this formId - ${opportunityId}`,
          requestId,
          '',
          'opportunityId',
          opportunityId,
          null,
          usecase,
        );
        this.log(
          loggerEnum.Event.OPERATION_COMPLETED,
          { opportunityId },
          { opportunityId },
          'Operation Completed',
          requestId,
          '',
          'opportunityId',
          opportunityId,
          opportunityFiles,
          usecase,
        );
        return {
          message: `No Documents found for this formId - ${opportunityId}`,
        };
      }
      if (opportunityFiles === 'Unauthorized') {
        throw new UnauthorizedException('Unauthorized');
      }

      this.log(
        loggerEnum.Event.COMPLETED_OPPORTUNITY_FILES,
        { opportunityId },
        { opportunityId },
        'Get opportunity files completed',
        requestId,
        '',
        'opportunityId',
        opportunityId,
        opportunityFiles,
        usecase,
      );
      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        { opportunityId },
        { opportunityId },
        'Operation Completed',
        requestId,
        '',
        'opportunityId',
        opportunityId,
        opportunityFiles,
        usecase,
      );
      return { response: opportunityFiles };
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_OPPORTUNITY_FILES,
        { opportunityId },
        { opportunityId },
        error.message ? error.message : JSON.stringify(error),
        requestId,
        '',
        'opportunityId',
        opportunityId,
        null,
        usecase,
      );
      throw error;
    }
  }
  async getOpportunityFilesS3SignedUrl(file, request?): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];
    try {
      this.log(
        loggerEnum.Event.INITIATED_FILE_DOWNLOAD,
        file,
        file,
        'Get opportunity files signed url initiated',
        requestId,
        '',
        'OpportunityFile',
        file?.S3FileName__c,
        null,
        usecase,
      );
      if (file?.Opportunity__r?.BusinessUnitFilter__c) {
        const brandDetails = await this.getBrandDetails(
          file?.Opportunity__r?.BusinessUnitFilter__c,
        );
        if (
          (file?.LetterType__c !== null &&
            file?.LetterType__c !== undefined &&
            file?.Opportunity__r?.BusinessUnitFilter__c !== 'UEG') ||
          (file?.Opportunity__r?.BusinessUnitFilter__c === 'UEG' &&
            file?.DocumentSource__c === 'CampusNet')
        ) {
          const bucketName = file?.BucketName__c
            ? file?.BucketName__c
            : brandDetails.Item?.AdmissionLetter?.StorageBucket;
          const region = file.BucketName__c
            ? brandDetails.Item?.AdmissionLetter?.StorageBucket ===
              file?.BucketName__c
              ? brandDetails.Item?.AdmissionLetter?.StorageRegion
              : process.env.REGION
            : brandDetails.Item?.AdmissionLetter?.StorageRegion;
          if (bucketName && file?.S3FileName__c != null) {
            file['FilePath'] = await this.buildS3FilePath(
              file,
              bucketName,
              decodeURIComponent(file?.S3FileName__c),
              process.env.S3_BUCKET_ACCESS_ROLE_ARN,
              region,
            );
          }
        } else if (file?.S3FileName__c != null) {
          const bucketName = file?.BucketName__c
            ? file?.BucketName__c
            : brandDetails.Item?.StudentDocument?.StorageBucket;
          const region = file.BucketName__c
            ? brandDetails.Item?.StudentDocument?.StorageBucket ===
              file?.BucketName__c
              ? brandDetails.Item?.StudentDocument?.StorageRegion
              : process.env.REGION
            : brandDetails.Item?.StudentDocument?.StorageRegion;
          if (bucketName) {
            file['FilePath'] = await this.buildS3FilePath(
              file,
              bucketName,
              decodeURIComponent(file?.S3FileName__c),
              process.env.S3_BUCKET_ACCESS_ROLE_ARN,
              region,
            );
          }
        }
      }
      this.log(
        loggerEnum.Event.COMPLETED_FILE_DOWNLOAD,
        file,
        file,
        'Get opportunity files signed url completed',
        requestId,
        '',
        'OpportunityFile',
        file?.S3FileName__c,
        file,
        usecase,
      );
      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        file,
        {},
        'Operation completed successfully',
        requestId,
        '',
        'OpportunityFile',
        file?.S3FileName__c,
        null,
        usecase,
      );
      return file;
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_FILE_DOWNLOAD,
        file,
        file,
        error.message ? error.message : JSON.stringify(error),
        requestId,
        '',
        'OpportunityFile',
        file?.S3FileName__c,
        null,
        usecase,
      );
      throw error;
    }
  }

  async getFileExtension(fileName): Promise<string> {
    const parts = fileName.split('.');
    if (parts.length === 1) {
      return '';
    }
    return parts[parts.length - 1];
  }

  async buildS3FilePath(
    file,
    bucket,
    path,
    roleArn,
    region = null,
  ): Promise<any> {
    const s3 = await this.s3Service.getS3CredentialsByRole(roleArn, region);
    const fileName = `"${encodeURIComponent(file.OriginalValue__c)}"`;
    const params = {
      Bucket: bucket,
      Key: path,
      Expires: 3600,
      ResponseContentDisposition: `attachment; filename=${fileName}`,
    };
    const filePath = await new Promise((resolve, reject) => {
      s3.getSignedUrl('getObject', params, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });
    return filePath;
  }

  async closeDocumentSubmissionTask(payload: any, request: any): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];

    let brandDetails;
    if (payload.BusinessUnitFilter__c) {
      try {
        brandDetails = await this.getBrandDetails(
          payload.BusinessUnitFilter__c,
        );
      } catch (error) {
        console.log(error);
        throw error;
      }
    }

    const bucketName = brandDetails?.Item?.StudentDocument?.StorageBucket
      ? brandDetails.Item?.StudentDocument?.StorageBucket
      : process.env.REVIEW_CENTER_BUCKET_NAME;

    console.log('bucketName ->', bucketName);

    // Process all files in parallel - files are already uploaded to S3 via signed URLs
    const processPromises = payload.files.map(async (fileData: any) => {
      // Use the objectKey provided from frontend (file already uploaded to S3)
      const objectKey = fileData.objectKey;
      console.log('Using existing S3 objectKey ->', objectKey);

      let createOpportunityFilePayload = {
        ApplicationId__c: payload?.applicationFormId,
        DocumentType__c: fileData?.documentType,
        Name: fileData?.fileName,
        FilePath__c: fileData?.fileName,
        Opportunity__c: payload.opportunityId,
        FullUrl__c: objectKey,
        OriginalValue__c: fileData?.fileName,
        DocumentSource__c: 'AppHero',
        S3FileName__c: objectKey,
        BucketName__c: bucketName,
      };

      if (payload.relatedField && payload.relatedFieldId) {
        createOpportunityFilePayload = {
          ...createOpportunityFilePayload,
          [payload.relatedField]: payload.relatedFieldId,
        };
      } else if (payload.relatedField) {
        console.log(
          `No related field Id found for ${payload.email} - ${payload.taskId}`,
        );
      } else {
        console.log(
          `No OpportunityFile linking field found ${payload.email} - ${payload.taskId}`,
        );
      }

      return {
        objectKey,
        fileName: fileData.fileName,
        documentType: fileData.documentType,
        createOpportunityFilePayload,
      };
    });

    // Log the initiation of parallel processing
    this.log(
      loggerEnum.Event.INITIATED_CLOSE_TASK,
      payload,
      { fileCount: payload.files.length },
      'Close document submission task initiated (parallel processing)',
      requestId,
      '',
      'opportunityId',
      payload.opportunityId,
      null,
      usecase,
    );

    // Wait for all processing to complete
    const results = await Promise.all(processPromises);

    try {
      // Create composite request for all files
      let compositeRequest: CompositeRequestItem[] = [];

      // Add OpportunityFile creation requests for each file
      results.forEach((result, index) => {
        compositeRequest.push({
          method: 'POST',
          url: '/services/data/v60.0/sobjects/OpportunityFile__c',
          referenceId: `OpportunityFile_${index}`,
          body: result.createOpportunityFilePayload,
        });
      });

      // Add task update request
      compositeRequest.push({
        method: 'PATCH',
        url: `/services/data/v60.0/sobjects/Task/${payload.taskId}`,
        referenceId: `TaskUpdate`,
        body: {
          Status: 'Completed',
        },
      });

      this.log(
        loggerEnum.Event.INITIATED_CLOSE_TASK_COMPOSITE_REQUEST,
        payload,
        compositeRequest,
        'Close document submission task composite request (parallel processing)',
        requestId,
        '',
        'opportunityId',
        payload.opportunityId,
        null,
        usecase,
      );

      console.log(
        'Composite Request -->',
        JSON.stringify(compositeRequest, null, 2),
      );

      const salesforceresponse = await this.salesforceService.postData(
        'gus/composite',
        { compositeRequest, dependsOnPriorCall: true },
      );
      console.log(
        'salesforceresponse -->',
        JSON.stringify(salesforceresponse, null),
      );

      // Check if all OpportunityFile creations were successful
      const opportunityFileResponses =
        salesforceresponse.compositeResponse.filter((response: any) =>
          response.referenceId.startsWith('OpportunityFile_'),
        );

      const allFilesSuccessful = opportunityFileResponses.every(
        (response: any) => response.httpStatusCode === 201,
      );

      if (allFilesSuccessful) {
        // Update DynamoDB for each successful file upload
        const opportunityFileDetails = results.map((result, index) => {
          const response = opportunityFileResponses[index];
          return {
            applicationFormId: payload?.applicationFormId,
            documentType: result.documentType,
            documentId: result.documentId,
            fileName: result.fileName,
            BucketName:
              brandDetails?.Item?.StudentDocument?.StorageBucket ||
              process.env.REVIEW_CENTER_BUCKET_NAME,
            opportunityFileId: response.body.id,
            s3ObjectKey: result.objectKey,
          };
        });

        // Prepare bulk data for DynamoDB batch operation
        const opportunityFileItems = opportunityFileDetails.map(
          (detail, index) => ({
            PK: payload?.opportunityId,
            SK: detail.opportunityFileId,
            ...results[index].createOpportunityFilePayload,
            documentId: detail.documentId,
            createdAt: new Date().toISOString(),
          }),
        );

        // Batch upload to DynamoDB instead of sequential operations
        await this.dynamoDBService.uploadBulkDataToDynamoDB(
          opportunityFileItems,
          process.env.APPHERO_SF_OPPORTUNITYFILE_TABLE,
        );

        console.log('All OpportunityFiles created successfully');

        // Parallel execution of task update and notification check

        await this.dynamoDBService.updateObject(
          process.env.APPHERO_TASK_TABLE,
          {
            PK: payload?.opportunityId,
            SK: payload.taskId,
          },
          {
            Status: 'Completed',
            updatedAt: new Date().toISOString(),
            isDocumentUploaded: true,
            s3ObjectKeys: results.map((r) => r.objectKey),
            opportunityFileDetails: opportunityFileDetails,
          },
        );

        const openTaskDetails = await this.getOpenTasksByOpportunityId(
          payload?.opportunityId,
        );

        console.log('openTasks -->', openTaskDetails.length);
        if (openTaskDetails.length === 0) {
          await this.notificationContentBuilder.closeNotification(
            payload?.email,
          );
        }
      } else {
        console.error(
          `Failed to process OpportunityFiles for task ${payload.taskId}`,
        );
        throw new Error(
          `Failed to process OpportunityFiles for task ${payload.taskId}`,
        );
      }

      this.log(
        loggerEnum.Event.COMPLETED_CLOSE_TASK,
        payload,
        compositeRequest,
        'Close document submission task completed (parallel processing)',
        requestId,
        '',
        'opportunityId',
        payload.opportunityId,
        {
          keys: results.map((r) => r.objectKey),
        },
        usecase,
      );
      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        payload,
        {},
        'Operation completed successfully (parallel processing)',
        requestId,
        '',
        'opportunityId',
        payload.opportunityId,
        null,
        usecase,
      );
      return {
        keys: results.map((r) => r.objectKey),
        opportunityFileDetails: results.map((result, index) => {
          const response = opportunityFileResponses[index];
          return {
            applicationFormId: payload?.applicationFormId,
            documentType: result.documentType,
            documentId: result.documentId,
            fileName: result.fileName,
            BucketName:
              brandDetails?.Item?.StudentDocument?.StorageBucket ||
              process.env.REVIEW_CENTER_BUCKET_NAME,
            opportunityFileId: response.body.id,
            s3ObjectKey: result.objectKey,
          };
        }),
        uploadedFiles: results.length,
      };
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_CLOSE_TASK,
        payload,
        payload,
        error.message ? error.message : JSON.stringify(error),
        requestId,
        '',
        'opportunityId',
        payload.opportunityId,
        null,
        usecase,
      );
      throw new Error(`Failed to upload files: ${error.message}`);
    }
  }

  async generateSignedUrl(path, toDownload, request?): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];
    try {
      this.log(
        loggerEnum.Event.INITITAED_GENERATE_SIGNED_URL,
        path,
        path,
        'generate signed url initiated',
        requestId,
        '',
        'OpportunityFile',
        '',
        null,
        usecase,
      );
      const s3 = await this.s3Service.getS3CredentialsByRole(
        process.env.S3_BUCKET_ACCESS_ROLE_ARN,
      );
      const params = {
        Bucket: process.env.REVIEW_CENTER_BUCKET_NAME,
        Key: path,
        Expires: 3600,
      };

      if (toDownload) {
        params['ResponseContentDisposition'] = 'attachment';
      } else {
        params['ResponseContentDisposition'] = 'inline';
      }

      const signedUrl = await new Promise((resolve, reject) => {
        s3.getSignedUrl('getObject', params, (err, data) => {
          if (err) reject(err);
          else resolve(data);
        });
      });
      this.log(
        loggerEnum.Event.COMPLETED_GENERATE_SIGNED_URL,
        path,
        path,
        'generate signed url completed',
        requestId,
        '',
        'OpportunityFile',
        '',
        { signedUrl },
        usecase,
      );
      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        path,
        path,
        'Operation completed',
        requestId,
        '',
        'OpportunityFile',
        '',
        { signedUrl },
        usecase,
      );
      return { signedUrl };
    } catch (error) {
      console.log('error -->', error);
      this.error(
        loggerEnum.Event.FAILED_GENERATE_SIGNED_URL,
        path,
        path,
        error.message ? error.message : JSON.stringify(error),
        requestId,
        '',
        'OpportunityFile',
        '',
        null,
        usecase,
      );
      throw error;
    }
  }

  async getOpenTasksByOpportunityId(opportunityId: string): Promise<any> {
    let params = {
      TableName: process.env.APPHERO_TASK_TABLE,
      KeyConditionExpression: `PK = :pk`,
      FilterExpression: `#Status = :status AND (contains(Subject, :subject1) OR contains(Subject, :subject2) OR contains(Subject, :subject3) OR contains(Subject, :subject4) OR contains(Subject, :subject5))`,
      ExpressionAttributeNames: {
        '#Status': 'Status',
      },
      ExpressionAttributeValues: {
        ':pk': opportunityId,
        ':status': 'Open',
        ':subject1': 'Review Center Comments',
        ':subject2': 'Review Center Comment',
        ':subject3': 'Smart Apply Tasks',
        ':subject4': 'Institution Comments',
        ':subject5': 'UCW FOLLOWUP TASK',
      },
    };
    console.log('params', params);
    const opportunityTasks = await this.dynamoDBService.queryObjects(params);
    console.log('opportunityTasks -->', opportunityTasks.Items);

    return opportunityTasks.Items;
  }

  async getTasksByEmail(input: { email: string }) {
    try {
      const appheroSupportedBrand =
        await this.dynamoDBService.getAppheroSupportedBrand();
      const draftAllowedBrands =
        await this.dynamoDBService.getAppheroDraftSupportedBrand();

      const {
        filterExpression,
        expressionAttributeNames,
        expressionAttributeValues,
      } = this.buildBrandFilterExpression(
        appheroSupportedBrand,
        draftAllowedBrands,
      );

      const queryParams: any = {
        TableName: process.env.APPHERO_SF_OPPORTUNITY_TABLE,
        KeyConditionExpression: 'PK = :pk',
        FilterExpression: filterExpression,
        ExpressionAttributeValues: {
          ':pk': input.email.toLowerCase(),
          ...expressionAttributeValues,
        },
        ExpressionAttributeNames: expressionAttributeNames,
        ProjectionExpression: 'Id,PK',
      };

      const opportunities = await this.dynamoDBService.queryObjects(
        queryParams,
      );

      if (opportunities.Items.length === 0) {
        opportunities.Items.push(
          ...(await this.getOpportunitiesByEmailFromSf(input)),
        );
      }

      if (opportunities.Items.length > 0) {
        const midpoint = Math.ceil(opportunities.Items.length / 2);

        // Fetch tasks and events in parallel
        const processOpportunitiesBatch = async (
          opportunitiesBatch,
          batchName,
        ) => {
          console.log(`${batchName} started at: ${new Date().toISOString()}`);

          const results = await Promise.all(
            opportunitiesBatch.map(async (opportunity) => {
              const [taskItems]: [any] = await Promise.all([
                this.getReviewCenterTaskByOpportunityId(opportunity.Id),
              ]);

              opportunity.ActionRequiredItems = taskItems;
              opportunity.ActionRequiredCount = taskItems?.Items?.filter(
                (item) => item.Status === 'Open',
              ).length;

              return opportunity;
            }),
          );

          console.log(`${batchName} finished at: ${new Date().toISOString()}`);
          return results;
        };

        const allResults =
          opportunities.Items.length === 1
            ? await processOpportunitiesBatch(
                opportunities.Items,
                'Single Batch',
              )
            : await Promise.all([
                processOpportunitiesBatch(
                  opportunities.Items.slice(0, midpoint),
                  'First Batch',
                ),
                processOpportunitiesBatch(
                  opportunities.Items.slice(midpoint),
                  'Second Batch',
                ),
              ]);

        const processedOpportunities = allResults.flat();

        processedOpportunities.sort(this.sortList);

        return { response: processedOpportunities };
      } else {
        return { response: [] };
      }
    } catch (error) {
      console.log('Error:', error);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  async log(
    apiEvent,
    sourcePayload,
    destinationPayload,
    logMessage,
    requestId?,
    email?,
    entityKey?,
    entityKeyField?,
    response?,
    usecase?,
  ) {
    await this.loggerService.log(
      requestId || uuidv4(),
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      loggerEnum.UseCase[`${usecase}`],
      sourcePayload,
      destinationPayload,
      logMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }

  async error(
    apiEvent,
    sourcePayload,
    destinationPayload,
    errorMessage,
    requestId?,
    email?,
    entityKey?,
    entityKeyField?,
    response?,
    usecase?,
  ) {
    await this.loggerService.error(
      requestId || uuidv4(),
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      loggerEnum.UseCase[`${usecase}`],
      sourcePayload,
      destinationPayload,
      errorMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }

  async mapIntakeDate(opportunity: any) {
    opportunity.IntakeDate =
      opportunity?.OverallStartDate__c ||
      opportunity?.Product_Intake_Date__c ||
      opportunity?.OpportunityLineItems?.records?.[0]?.Intake_Date__c ||
      null;
  }

  async refreshOpportunityFromSalesforce(
    email: string,
    opportunityId: string,
  ): Promise<any> {
    try {
      if (!opportunityId || !email) {
        console.log('Missing email or opportunityId');
        return;
      }
      // Check if the opportunity exists in DynamoDB
      const existingOpportunity = await this.dynamoDBService.getObject(
        process.env.APPHERO_SF_OPPORTUNITY_TABLE,
        {
          PK: email,
          SK: opportunityId,
        },
      );

      if (!existingOpportunity.Item) {
        console.log(
          `No opportunity found for email ${email} and Id ${opportunityId} in DB`,
        );
      }

      // Fetch the latest data from Salesforce
      console.log('Fetching latest data from Salesforce...');
      const response = await this.salesforceService.fetchData(
        `gus/getOpportunitiesById/${opportunityId}`,
      );

      const opportunityDetails = response[0];
      if (!opportunityDetails) {
        console.log(
          `No opportunity found in Salesforce for Id - ${opportunityId}`,
        );
      }

      // Update the opportunity in DynamoDB
      console.log('Updating opportunity in DynamoDB...');
      const params = {
        Item: {
          PK: email,
          SK: opportunityId,
          updatedAt: new Date().toISOString(),
          ...opportunityDetails,
        },
      };

      await this.dynamoDBService.putObject(
        process.env.APPHERO_SF_OPPORTUNITY_TABLE,
        params,
      );

      console.log('Opportunity refreshed successfully in DynamoDB');

      return true;
    } catch (error) {
      console.log('Error when refreshing opportunity details from SF:', error);
    }
  }

  async getSignedUrlToUploadDoc(docDetails, request): Promise<any> {
    const currentUTC = new Date().toISOString();
    const usecase = request?.headers?.['x-screen-name'];
    try {
      const documentId = uuidv4();
      const objectKey = docDetails.applicationFormId
        ? `${docDetails.applicationFormId}/${
            docDetails?.documentType
          }/${documentId}.${await this.getFileExtension(docDetails?.fileName)}`
        : `${docDetails.opportunityId}/${
            docDetails?.documentType
          }/${documentId}.${await this.getFileExtension(docDetails?.fileName)}`;

      console.log('Document details -->', docDetails);

      console.log('Object Key -> ', objectKey);

      let brandDetails;
      if (docDetails.BusinessUnitFilter__c) {
        try {
          brandDetails = await this.getBrandDetails(
            docDetails.BusinessUnitFilter__c,
          );
        } catch (error) {
          console.log(error);
          throw error;
        }
      }

      const bucketName = brandDetails?.Item?.StudentDocument?.StorageBucket
        ? brandDetails.Item?.StudentDocument?.StorageBucket
        : process.env.REVIEW_CENTER_BUCKET_NAME;

      return {
        signedUrl: await this.buildUploadFileSignedUrl(
          bucketName,
          objectKey,
          process.env.S3_BUCKET_ACCESS_ROLE_ARN,
          docDetails.contentType,
        ),
        objectKey,
      };
    } catch (error) {
      await this.loggerService.error(
        request.requestId,
        currentUTC,
        loggerEnum.Component.APPHERO_BACKEND,
        loggerEnum.Component.APPHERO_FRONTEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.UPLOAD_DOCUMENT,
        usecase,
        docDetails,
        docDetails,
        error,
        request?.brand,
        docDetails.email ||
          docDetails.applicationFormId ||
          docDetails.opportunityId,
        'Application_Form_Id__c',
        docDetails.applicationFormId,
      );
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  async buildUploadFileSignedUrl(
    bucket,
    path,
    roleArn,
    contentType,
  ): Promise<any> {
    try {
      const s3 = await this.s3Service.getS3CredentialsByRole(roleArn);
      const params = {
        Bucket: bucket,
        Key: path,
        Expires: 3600,
        ContentType: contentType,
        ACL: 'public-read',
      };
      const filePath = await new Promise((resolve, reject) => {
        s3.getSignedUrl('putObject', params, (err, data) => {
          if (err) reject(err);
          else resolve(data);
        });
      });
      return filePath;
    } catch (error) {
      throw error;
    }
  }

  async deleteFile(docDetails, request): Promise<any> {
    const objectKey = docDetails.objectKey;
    const s3 = await this.s3Service.getS3CredentialsByRole(
      process.env.S3_BUCKET_ACCESS_ROLE_ARN,
    );

    let brandDetails;
    if (docDetails.BusinessUnitFilter__c) {
      try {
        brandDetails = await this.getBrandDetails(
          docDetails.BusinessUnitFilter__c,
        );
      } catch (error) {
        console.log(error);
        throw error;
      }
    }

    const bucketName = brandDetails?.Item?.StudentDocument?.StorageBucket
      ? brandDetails.Item?.StudentDocument?.StorageBucket
      : process.env.REVIEW_CENTER_BUCKET_NAME;

    const params = {
      Bucket: bucketName,
      Key: objectKey,
    };

    try {
      await s3.deleteObject(params).promise();
      console.log(
        `File ${objectKey} deleted successfully from bucket ${bucketName}.`,
      );
      return { success: true };
    } catch (err) {
      console.error(
        `Error deleting file ${objectKey} from bucket ${bucketName} or from ${process.env.STUDENT_DOCUMENTS} table:`,
        err,
      );
      throw err;
    }
  }
}

type CompositeRequestItem = {
  method: string;
  url: string;
  referenceId: string;
  body: any;
};
