import { Injectable } from '@nestjs/common';
import axios, { AxiosError } from 'axios';
import {
  SecretsManagerClient,
  GetSecretValueCommand,
  UpdateSecretCommand,
} from '@aws-sdk/client-secrets-manager';
import { DirectSalesforceQueryBuilder } from './direct-salesforce-query.builder';
import { DynamoDBService } from './dynamodb.service';

@Injectable()
export class DirectSalesforceService {
  private consumerKey: string;
  private consumerSecret: string;
  private authUrl: string;
  private awsSecret: string;
  private grantType: string;
  private userName: string;
  private password: string;
  private secretClient: SecretsManagerClient;
  private readonly dynamoDBService: DynamoDBService;

  constructor(dynamoDBService: DynamoDBService) {
    this.dynamoDBService = dynamoDBService;
    this.consumerKey = process.env.GUS_CONSUMER_KEY;
    this.consumerSecret = process.env.GUS_CONSUMER_SECRET;
    this.authUrl = process.env.GUS_AUTH_URL;
    // Use the correct AWS Secrets Manager secret name based on environment
    // Dev: salesforce-gus-dev-access-token, Prod: salesforce-gus-prod-access-token
    this.awsSecret =
      process.env.GUS_ACCESS_TOKEN_SECRET ||
      `salesforce-gus-${process.env.STAGE}-access-token`;
    this.grantType = process.env.GUS_GRANT_TYPE;
    this.userName = process.env.GUS_USER_NAME;
    this.password = process.env.GUS_PASSWORD;

    this.secretClient = new SecretsManagerClient({
      region: process.env.REGION,
    });
  }

  async getAWSSecret(secretName: string): Promise<any> {
    try {
      const response = await this.secretClient.send(
        new GetSecretValueCommand({
          SecretId: secretName,
        }),
      );
      return JSON.parse(response.SecretString);
    } catch (error) {
      console.error('Error retrieving secret:', error);
      return null;
    }
  }

  async updateSecret(secretName: string, secretValue: any): Promise<void> {
    try {
      await this.secretClient.send(
        new UpdateSecretCommand({
          SecretId: secretName,
          SecretString: JSON.stringify(secretValue),
        }),
      );
    } catch (error) {
      console.error('Error updating secret:', error);
    }
  }

  async getCredentials(isTokenExpired = false): Promise<any> {
    let response;
    const secretKeyResponse = await this.getAWSSecret(this.awsSecret);

    if (!secretKeyResponse || isTokenExpired) {
      response = await axios.post(this.authUrl, null, {
        params: {
          grant_type: this.grantType,
          client_id: this.consumerKey,
          client_secret: this.consumerSecret,
          username: this.userName,
          password: this.password,
        },
      });

      await this.updateSecret(this.awsSecret, response.data);
      return response.data;
    }

    return secretKeyResponse;
  }

  async buildAxiosConfig(
    endpoint: string,
    method: string,
    payload = null,
  ): Promise<any> {
    const clientCredentials = await this.getCredentials();
    const rootPath = `${clientCredentials.instance_url}/services/data/${process.env.SALESFORCE_API_VERSION}/`;

    switch (method) {
      case 'POST':
      case 'PATCH':
        return {
          url: `${rootPath}${endpoint}`,
          method,
          headers: {
            Authorization: `Bearer ${clientCredentials.access_token}`,
            'Content-Type': 'application/json',
          },
          data: payload,
        };
      case 'GET':
        return {
          url: `${rootPath}${endpoint}`,
          method: 'GET',
          headers: {
            Authorization: `Bearer ${clientCredentials.access_token}`,
          },
        };
      case 'DELETE':
        return {
          url: `${rootPath}${endpoint}`,
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${clientCredentials.access_token}`,
          },
        };
      default:
        throw new Error(`Unsupported HTTP method: ${method}`);
    }
  }

  async executeAPI(
    apiPath: string,
    method: string,
    payload = null,
  ): Promise<any> {
    try {
      console.log('apiPath: Start Time: ', new Date().toISOString());
      const axiosConfig = await this.buildAxiosConfig(apiPath, method, payload);
      const response = await axios(axiosConfig);
      console.log('apiPath: End Time: ', new Date().toISOString());
      return response.data;
    } catch (error) {
      console.log('error: ', error);
      if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError;
        if (axiosError.response && axiosError.response.status === 401) {
          console.log(
            'Got a 401 Unauthorized error. Refreshing access token...',
          );
          await this.getCredentials(true);
          return this.executeAPI(apiPath, method, payload);
        }
        if (axiosError.response) {
          console.error(
            'Server responded with error:',
            axiosError.response.data,
          );
          console.error('HTTP status code:', axiosError.response.status);
          throw { message: axiosError.response.data };
        }
        throw { message: axiosError.message };
      }
      throw error;
    }
  }

  // Direct Salesforce API methods to replace middleware calls
  async getOpportunityById(opportunityId: string): Promise<any> {
    const encodedQuery = DirectSalesforceQueryBuilder.getOptimizedQuery(
      'opportunityById',
      {
        opportunityId,
        appheroSupportedBrands:
          await this.dynamoDBService.getAppheroSupportedBrand(),
      },
    );

    return this.executeAPI(`query?q=${encodedQuery}`, 'GET');
  }

  async getPersonAccountByEmail(email: string): Promise<any> {
    const encodedQuery = DirectSalesforceQueryBuilder.buildAccountByEmailQuery(
      email,
      {
        appheroSupportedBrands:
          await this.dynamoDBService.getAppheroSupportedBrand(),
      },
    );

    return this.executeAPI(`query?q=${encodedQuery}`, 'GET');
  }

  async updateAccount(accountData: any): Promise<any> {
    const { Ids } = accountData;

    const updatePayload = {
      App_Hero_Consent__c: true,
    };

    if (!Ids || Ids.length === 0) {
      throw new Error('No account IDs provided for update');
    }

    console.log(
      `Updating ${Ids.length} accounts - Start Time:`,
      new Date().toISOString(),
    );

    // Use different strategies based on the number of accounts
    if (Ids.length === 1) {
      // Single account - use direct API call
      return this.updateSingleAccount(Ids[0], updatePayload);
    } else if (Ids.length <= 200) {
      console.log('Using batch update strategy');
      // Small to medium batch - use Salesforce Composite SObjects API (most efficient)
      return this.updateAccountsBatch(Ids, updatePayload);
    } else {
      // Large batch - process in chunks using Composite SObjects API
      return this.updateAccountsLargeBatch(Ids, updatePayload);
    }
  }

  private async updateSingleAccount(
    accountId: string,
    updatePayload: any,
  ): Promise<any> {
    try {
      const result = await this.executeAPI(
        `sobjects/Account/${accountId}`,
        'PATCH',
        updatePayload,
      );
      return [{ id: accountId, success: true, result }];
    } catch (error) {
      return [{ id: accountId, success: false, error }];
    }
  }

  private async updateAccountsBatch(
    accountIds: string[],
    updatePayload: any,
  ): Promise<any> {
    try {
      // Prepare records for Composite SObjects API
      const records = accountIds.map((id) => ({
        attributes: { type: 'Account' },
        Id: id,
        ...updatePayload,
      }));

      // Use Composite SObjects API for batch update
      const batchResponse = await this.executeAPI(
        'composite/sobjects',
        'PATCH',
        {
          allOrNone: false,
          records: records,
        },
      );

      // Transform response to match original format
      return accountIds.map((id, index) => {
        const responseItem = batchResponse[index];
        if (responseItem.success) {
          return { id, success: true, result: responseItem };
        } else {
          return { id, success: false, error: responseItem.errors };
        }
      });
    } catch (error) {
      console.error(
        'Batch update failed, falling back to parallel processing:',
        error,
      );
      // Fallback to parallel processing if batch API fails
      return this.updateAccountsParallel(accountIds, updatePayload);
    }
  }

  private async updateAccountsLargeBatch(
    accountIds: string[],
    updatePayload: any,
  ): Promise<any> {
    const BATCH_SIZE = 200; // Salesforce Composite SObjects API limit
    const results = [];

    console.log(
      `Processing ${accountIds.length} accounts in batches of ${BATCH_SIZE}`,
    );

    // Process in chunks of 200
    for (let i = 0; i < accountIds.length; i += BATCH_SIZE) {
      const batch = accountIds.slice(i, i + BATCH_SIZE);
      console.log(
        `Processing batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(
          accountIds.length / BATCH_SIZE,
        )}`,
      );

      const batchResults = await this.updateAccountsBatch(batch, updatePayload);
      console.log(
        `Completed batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(
          accountIds.length / BATCH_SIZE,
        )}`,
      );
      results.push(...batchResults);
    }

    console.log(
      `Completed updating ${accountIds.length} accounts - End Time:`,
      new Date().toISOString(),
    );
    return results;
  }

  private async updateAccountsParallel(
    accountIds: string[],
    updatePayload: any,
  ): Promise<any> {
    console.log(
      `Fallback: Processing ${accountIds.length} accounts in parallel`,
    );

    // Use Promise.all for parallel processing (fallback method)
    const updatePromises = accountIds.map(async (id) => {
      try {
        const result = await this.executeAPI(
          `sobjects/Account/${id}`,
          'PATCH',
          updatePayload,
        );
        return { id, success: true, result };
      } catch (error) {
        return { id, success: false, error };
      }
    });

    return Promise.all(updatePromises);
  }

  async getOpportunityFile(opportunityFileId: string): Promise<any> {
    const encodedQuery =
      DirectSalesforceQueryBuilder.buildOpportunityFileByIdQuery(
        opportunityFileId,
      );

    return this.executeAPI(`query?q=${encodedQuery}`, 'GET');
  }

  async getOpportunitiesByEmail(email: string): Promise<any> {
    const encodedQuery = DirectSalesforceQueryBuilder.getOptimizedQuery(
      'opportunitiesByEmail',
      {
        email,
        appheroSupportedBrands:
          await this.dynamoDBService.getAppheroSupportedBrand(),
      },
    );

    return this.executeAPI(`query?q=${encodedQuery}`, 'GET');
  }
}
