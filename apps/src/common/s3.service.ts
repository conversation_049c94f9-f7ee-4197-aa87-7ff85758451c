import { Injectable } from '@nestjs/common';
import * as AWS from 'aws-sdk';
const s3 = new AWS.S3();
const sts = new AWS.STS();
@Injectable()
export class S3Service {
  async uploadObject(params): Promise<any> {
    const result = await s3.upload(params).promise();
    return result;
  }
  async getHtmlFileFromS3(params): Promise<string> {
    console.log("Params -->", params)
    try {
      const data = await s3.getObject(params).promise();
      const htmlContent = data.Body?.toString('utf-8') || '';
      return htmlContent;
    } catch (error) {
      console.error('Error fetching file from S3:', error);
      throw new Error('Could not retrieve HTML file from S3');
    }
  }

  async getS3CredentialsByRole(
    roleArn: string,
    region: string = process.env.REGION,
  ): Promise<AWS.S3> {
    const sessionName = `Session-${Date.now()}`;
    const sts = new AWS.STS({ region });
    const param: AWS.STS.AssumeRoleRequest = {
      RoleArn: roleArn,
      RoleSessionName: sessionName,
    };
    const data: AWS.STS.AssumeRoleResponse = await new Promise(
      (resolve, reject) => {
        sts.assumeRole(param, (err, data) => {
          if (err) reject(err);
          else resolve(data);
        });
      },
    );

    const credentials = data.Credentials;

    const s3 = new AWS.S3({
      accessKeyId: credentials.AccessKeyId,
      secretAccessKey: credentials.SecretAccessKey,
      sessionToken: credentials.SessionToken,
      region: region,
      signatureVersion: 'v4',
    });
    return s3;
  }
}
