import {
  DirectSalesforceFieldsConfig,
  FieldConfig,
  WhereClauseConfig,
} from './direct-salesforce-fields.config';

/**
 * Query builder for Direct Salesforce Service
 * Provides environment-aware query construction with dynamic field selection
 */
export class DirectSalesforceQueryBuilder {
  private fields = [];
  private fromObject = '';
  private whereConditions = [];
  private orderByClause = '';
  private limitClause = '';

  constructor() {
    this.reset();
  }

  /**
   * Reset the query builder to initial state
   */
  reset(): DirectSalesforceQueryBuilder {
    this.fields = [];
    this.fromObject = '';
    this.whereConditions = [];
    this.orderByClause = '';
    this.limitClause = '';
    return this;
  }

  /**
   * Set fields from a predefined configuration
   */
  selectFromConfig(config: FieldConfig): DirectSalesforceQueryBuilder {
    this.fields = DirectSalesforceFieldsConfig.getCompleteFields(config);
    return this;
  }

  /**
   * Add custom fields to the selection
   */
  select(fields: string[]): DirectSalesforceQueryBuilder {
    this.fields.push(...fields);
    return this;
  }

  /**
   * Set the FROM object
   */
  from(objectName: string): DirectSalesforceQueryBuilder {
    this.fromObject = objectName;
    return this;
  }

  /**
   * Add WHERE conditions from configuration
   */
  whereFromConfig(
    whereConfig: WhereClauseConfig,
  ): DirectSalesforceQueryBuilder {
    if (whereConfig.conditions) {
      this.whereConditions.push(...whereConfig.conditions);
    }
    return this;
  }

  /**
   * Add custom WHERE condition
   */
  where(condition: string): DirectSalesforceQueryBuilder {
    this.whereConditions.push(condition);
    return this;
  }

  /**
   * Add ORDER BY clause
   */
  orderBy(orderBy: string): DirectSalesforceQueryBuilder {
    this.orderByClause = orderBy;
    return this;
  }

  /**
   * Add LIMIT clause
   */
  limit(limitValue: number): DirectSalesforceQueryBuilder {
    this.limitClause = `LIMIT ${limitValue}`;
    return this;
  }

  /**
   * Build the complete SOQL query
   */
  build(): string {
    if (!this.fromObject) {
      throw new Error('FROM object is required');
    }

    if (this.fields.length === 0) {
      throw new Error('At least one field must be selected');
    }

    let query = `SELECT \n    ${this.fields.join(',\n    ')}\nFROM \n    ${
      this.fromObject
    }`;

    if (this.whereConditions.length > 0) {
      query += ` \nWHERE \n    ${this.whereConditions.join(' \n    AND ')}`;
    }

    if (this.orderByClause) {
      query += ` \nORDER BY ${this.orderByClause}`;
    }

    if (this.limitClause) {
      query += ` \n${this.limitClause}`;
    }

    return query;
  }

  /**
   * Build and encode the query for URL usage
   */
  buildEncoded(): string {
    return encodeURIComponent(this.build());
  }

  /**
   * Static method to build account by email query
   */
  static buildAccountByEmailQuery(email: string, options: any): string {
    return new DirectSalesforceQueryBuilder()
      .selectFromConfig(DirectSalesforceFieldsConfig.ACCOUNT_FIELDS.byEmail)
      .from('Account')
      .whereFromConfig(
        DirectSalesforceFieldsConfig.WHERE_CLAUSES.accountByEmail(
          email,
          options,
        ),
      )
      .buildEncoded();
  }

  static buildOpportunityFileByIdQuery(opportunityFileId: string): string {
    return new DirectSalesforceQueryBuilder()
      .selectFromConfig(
        DirectSalesforceFieldsConfig.OPPORTUNITY_FILE_FIELDS.byId,
      )
      .from('OpportunityFile__c')
      .whereFromConfig(
        DirectSalesforceFieldsConfig.WHERE_CLAUSES.opportunityFileById(
          opportunityFileId,
        ),
      )
      .buildEncoded();
  }

  static getOptimizedQuery(queryType: string, parameters: any): string {
    switch (queryType) {
      case 'opportunityById':
        // Production optimization: minimal fields for better performance
        return new DirectSalesforceQueryBuilder()
          .selectFromConfig(
            DirectSalesforceFieldsConfig.OPPORTUNITY_FIELDS.byId,
          )
          .from('Opportunity')
          .whereFromConfig(
            DirectSalesforceFieldsConfig.WHERE_CLAUSES.opportunityById(
              parameters.opportunityId,
              parameters,
            ),
          )
          .buildEncoded();

      case 'opportunitiesByEmail':
        // Production: add performance optimizations
        return new DirectSalesforceQueryBuilder()
          .selectFromConfig(
            DirectSalesforceFieldsConfig.OPPORTUNITY_FIELDS.byEmail,
          )
          .from('Opportunity')
          .whereFromConfig(
            DirectSalesforceFieldsConfig.WHERE_CLAUSES.opportunitiesByEmail(
              parameters.email,
              parameters,
            ),
          )
          .buildEncoded();

      default:
        throw new Error(`Unknown query type: ${queryType}`);
    }
  }
}
