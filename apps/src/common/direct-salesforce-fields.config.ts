/**
 * Field configurations for Direct Salesforce Service queries
 * Organized by object type and use case with environment-specific optimizations
 *
 * Updated to align with apphero-sync service requirements:
 * - Added missing fields: Student_Placement_Status__c, App_Hero_Consent__c, Account.Id
 * - Added Visa_Application__r subquery with all required fields
 * - Enhanced WHERE clause logic to match apphero-sync filtering patterns
 * - Added support for RecordType exclusions, ApplicationSource filtering, and time-based queries
 * - Maintained backward compatibility with existing configurations
 */

import { DynamoDBService } from './dynamodb.service';

export interface FieldConfig {
  base: string[];
  dev?: string[];
  prod?: string[];
  subqueries?: string[];
}

export interface WhereClauseConfig {
  conditions: string[];
  parameters?: Record<string, any>;
}

export interface OpportunitiesByEmailParams {
  email: string;
  appheroSupportedBrands?: string[];
  timeRange?: {
    startTime: string;
    endTime: string;
  };
  excludeRecordTypes?: string[];
  excludeApplicationSources?: string[];
}

export interface OpportunityByIdParams {
  opportunityId: string;
  appheroSupportedBrands?: string[];
  excludeRecordTypes?: string[];
  excludeApplicationSources?: string[];
}

export class DirectSalesforceFieldsConfig {
  // Environment-specific field configurations
  private static readonly ENVIRONMENT = process.env.STAGE;
  private dynamoDbService: DynamoDBService;

  // Opportunity field configurations
  static readonly OPPORTUNITY_FIELDS: Record<string, FieldConfig> = {
    byId: {
      base: [
        'Id',
        'Name',
        'toLabel(Brand__c)Institution',
        'Brand__c',
        'CreatedDate',
        'Account.Name',
        'Account.PersonEmail',
        'Account.Phone',
        'StageName',
        'OwnerName__c',
        'AgentContact__c',
        'ApplicationFormId__c',
        'BusinessDeveloperName__c',
        'Admissions_Condition__c',
        'AgentAccount__r.Email__c',
        'AgentAccount__r.Phone',
        'AgentAccountName__c',
        'Agent_Contact__r.Email',
        'Agent_Contact__r.Phone',
        'Agent_Contact__r.Name',
        'RecordType.Name',
        'ApplicationSource__c',
        'OwnerEmail__c',
        'Owner.Phone',
        'BusinessUnitFilter__c',
        'Institution_Full_Name__c',
        'Owner.Appointment_Booking_Link__c',
        'DeclarationDate__c',
        'ProgrammeName__c',
        'Delivery_Mode__c',
        'Application_Submitted_Date__c',
        'OverallStartDate__c',
      ],
      dev: [
        // Additional fields for development environment
        'CreatedBy.Name',
        'LastModifiedDate',
        'LastModifiedBy.Name',
      ],
      prod: [
        // Production-optimized fields (minimal set for performance)
      ],
      subqueries: [
        `(
            SELECT 
                Location__c,
                Intake_Date__c,
                Product2.Duration__c,
                Product2.ProgrammeName__c,
                Product2.Campus_Days__c 
            FROM OpportunityLineItems
        )`,
      ],
    },
    byEmail: {
      base: [
        'Owner.Phone',
        'OwnerEmail__c',
        'AgentAccount__r.Phone',
        'AgentAccount__r.Email__c',
        'Admissions_Condition__c',
        'BusinessDeveloperName__c',
        'ApplicationFormId__c',
        'AgentContact__c',
        'Account.Phone',
        'Account.Name',
        'Account.PersonEmail',
        'Account.Id',
        'Id',
        'ApplicationId__c',
        'Brand__c',
        'CreatedDate',
        'toLabel(Brand__c)Institution',
        'Product_Intake_Date__c',
        'toLabel(Location__c)location',
        'CreatedBy.Name',
        'StageName',
        'Name',
        'toLabel(AdmissionsStage__c)',
        'ApplicationSubmitted__c',
        'ApplicationProgress__c',
        'RecordType.Name',
        'Institution_Full_Name__c',
        'AgentAccountName__c',
        'OwnerName__c',
        'Agent_Contact__r.Email',
        'Agent_Contact__r.Phone',
        'Agent_Contact__r.Name',
        'ApplicationSource__c',
        'BusinessUnitFilter__c',
        'Owner.Appointment_Booking_Link__c',
        'DeclarationDate__c',
        'ProgrammeName__c',
        'Delivery_Mode__c',
        'Application_Submitted_Date__c',
        'OverallStartDate__c',
        'Student_Placement_Status__c',
      ],
      dev: [
        // Additional debugging fields for development
        'LastModifiedDate',
        'SystemModstamp',
      ],
      prod: [
        // Production optimizations - exclude heavy fields if needed
      ],
      subqueries: [
        `(
            SELECT
                Location__c,
                Intake_Date__c,
                Product2.Duration__c,
                Product2.ProgrammeName__c,
                Product2.Campus_Days__c
            FROM OpportunityLineItems
        )`,
        `(
            SELECT
                Id,
                Visa_Application_Status__c,
                Arrival_Date__c,
                Opportunity__c,
                Visa_Application_Date__c,
                Visa_Interview_Date__c,
                Visa_Number__c,
                Visa_Required__c,
                CreatedDate,
                Visa_Application_Reference_Number__c,
                LastModifiedDate
            FROM Visa_Application__r
        )`,
      ],
    },
  };

  // Account field configurations
  static readonly ACCOUNT_FIELDS: Record<string, FieldConfig> = {
    byEmail: {
      base: [
        'Id',
        'Location__pc',
        'Name',
        'Phone',
        'PersonEmail',
        'FirstName',
        'LastName',
        'App_Hero_Can_Apply__c',
        'App_Hero_Consent__c',
        'RecordTypeId',
      ],
      dev: ['CreatedDate', 'LastModifiedDate'],
      prod: [],
    },
  };

  // OpportunityFile field configurations
  static readonly OPPORTUNITY_FILE_FIELDS: Record<string, FieldConfig> = {
    byId: {
      base: [
        'Id',
        'Name',
        'OpportunityId',
        'ContentDocumentId',
        'CreatedDate',
        'ContentDocument.Title',
        'ContentDocument.FileType',
      ],
      dev: ['LastModifiedDate', 'CreatedBy.Name'],
      prod: [],
    },
  };

  // WHERE clause configurations
  static readonly WHERE_CLAUSES = {
    opportunityById: (
      opportunityId: string,
      params?: Partial<OpportunityByIdParams>,
    ): WhereClauseConfig => {
      const conditions = [`Id = '${opportunityId}'`];

      // Add RecordType exclusions (matching apphero-sync logic)
      const defaultExcludedRecordTypes = ['Agent Onboarding', 'Default'];
      const excludedRecordTypes =
        params?.excludeRecordTypes || defaultExcludedRecordTypes;
      if (excludedRecordTypes.length > 0) {
        excludedRecordTypes.forEach((recordType) => {
          conditions.push(`RecordType.name != '${recordType}'`);
        });
      }

      // Add ApplicationSource exclusions (matching apphero-sync logic)
      const defaultExcludedSources = [
        'GUS Core',
        'Link Generator',
        'Salesforce',
        'CORE Cross-Sell',
      ];
      const excludedSources =
        params?.excludeApplicationSources || defaultExcludedSources;
      if (excludedSources.length > 0) {
        const sourcesString = excludedSources
          .map((source) => `'${source}'`)
          .join(',');
        conditions.push(`ApplicationSource__c NOT IN (${sourcesString})`);
      }

      // Add BusinessUnitFilter support (apphero-sync uses dynamic brands)
      if (
        params?.appheroSupportedBrands &&
        params.appheroSupportedBrands.length > 0
      ) {
        const brandsString = params.appheroSupportedBrands
          .map((brand) => `'${brand}'`)
          .join(',');
        conditions.push(`BusinessUnitFilter__c IN (${brandsString})`);
      }

      return { conditions };
    },

    accountByEmail: (email: string, options: any): WhereClauseConfig => ({
      conditions: [
        `PersonEmail = '${email}' AND RecordTypeId = '${
          process.env.PERSON_ACCOUNT_RECORDTYPE_ID
        }' AND BusinessUnitFilter__c IN ('${options.appheroSupportedBrands?.join(
          "','",
        )}')`,
      ],
    }),

    opportunitiesByEmail: (
      email: string,
      params?: Partial<OpportunitiesByEmailParams>,
    ): WhereClauseConfig => {
      const conditions = [`AccountEmail__c = '${email}'`];

      // Add RecordType exclusions (matching apphero-sync logic)
      conditions.push(`RecordType.name != 'Agent Onboarding'`);
      conditions.push(`RecordType.name != 'Default'`);

      // Add ApplicationSource exclusions (matching apphero-sync logic)
      const defaultExcludedSources = [
        'GUS Core',
        'Link Generator',
        'Salesforce',
        'CORE Cross-Sell',
      ];
      const excludedSources =
        params?.excludeApplicationSources || defaultExcludedSources;
      if (excludedSources.length > 0) {
        const sourcesString = excludedSources
          .map((source) => `'${source}'`)
          .join(',');
        conditions.push(`ApplicationSource__c NOT IN (${sourcesString})`);
      }

      // Add BusinessUnitFilter support (apphero-sync uses dynamic brands)
      if (
        params?.appheroSupportedBrands &&
        params.appheroSupportedBrands.length > 0
      ) {
        const brandsString = params.appheroSupportedBrands
          .map((brand) => `'${brand}'`)
          .join(',');
        conditions.push(`BusinessUnitFilter__c IN (${brandsString})`);
      }

      return { conditions };
    },

    opportunityFileById: (opportunityFileId: string): WhereClauseConfig => ({
      conditions: [`Id = '${opportunityFileId}'`],
    }),
  };

  /**
   * Get environment-specific fields for a given configuration
   */
  static getFields(config: FieldConfig): string[] {
    const fields = [...config.base];

    // Add environment-specific fields
    if (this.ENVIRONMENT === 'dev' && config.dev) {
      fields.push(...config.dev);
    } else if (this.ENVIRONMENT === 'prod' && config.prod) {
      fields.push(...config.prod);
    }

    return fields;
  }

  /**
   * Get complete field list including subqueries
   */
  static getCompleteFields(config: FieldConfig): string[] {
    const fields = this.getFields(config);

    if (config.subqueries) {
      fields.push(...config.subqueries);
    }

    return fields;
  }

  /**
   * Build WHERE clause from configuration
   */
  static buildWhereClause(whereConfig: WhereClauseConfig): string {
    if (!whereConfig.conditions || whereConfig.conditions.length === 0) {
      return '';
    }

    return `WHERE ${whereConfig.conditions.join(' AND ')}`;
  }

  /**
   * Get apphero-sync compatible opportunity fields with all subqueries
   * This matches the exact field list used in apphero-sync service
   */
  static getAppheroSyncOpportunityFields(): string[] {
    const config = this.OPPORTUNITY_FIELDS.byEmail;
    return this.getCompleteFields(config);
  }

  /**
   * Get apphero-sync compatible account fields
   * This matches the exact field list used in apphero-sync service
   */
  static getAppheroSyncAccountFields(): string[] {
    const config = this.ACCOUNT_FIELDS.byEmail;
    return this.getFields(config);
  }
}
