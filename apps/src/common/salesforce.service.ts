import { Injectable } from '@nestjs/common';
import axios, { AxiosError } from 'axios';
@Injectable()
export class SalesforceService {
  async fetchData(endpoint): Promise<any> {
    try {
      const path = `${process.env.GUS_MIDDLEWARE_API}/salesforce/${endpoint}`;
      const headers = {
        'Content-Type': 'application/json',
        'x-api-key': process.env.GUS_MIDDLEWARE_API_KEY,
      };

      const response = await axios.get(path, {
        headers,
      });

      return response.data;
    } catch (error) {
      console.log('error: ', error);
      throw error?.response?.data;
    }
  }
  async postData(endpoint, requestData: any): Promise<any> {
    try {
      const path = `${process.env.GUS_MIDDLEWARE_API}/salesforce/${endpoint}`;
      const headers = {
        'x-api-key': process.env.GUS_MIDDLEWARE_API_KEY,
      };

      const response = await axios.post(path, requestData, { headers });

      return response.data;
    } catch (error) {
      throw error?.response?.data;
    }
  }
  async patchData(endpoint, requestData: any): Promise<any> {
    try {
      const path = `${process.env.GUS_MIDDLEWARE_API}/salesforce/${endpoint}`;
      const headers = {
        'x-api-key': process.env.GUS_MIDDLEWARE_API_KEY,
      };

      const response = await axios.patch(path, requestData, { headers });

      return response.data;
    } catch (error) {
      throw error?.response?.data;
    }
  }
}
