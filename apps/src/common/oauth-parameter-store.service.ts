import { Injectable } from '@nestjs/common';
import { SSMClient, GetParameterCommand } from '@aws-sdk/client-ssm';
import axios, { AxiosError } from 'axios';

export interface OAuthTokenResponse {
  access_token: string;
  token_type: string;
  expires_in?: number;
  scope?: string;
}

@Injectable()
export class OAuthParameterStoreService {
  private ssmClient: SSMClient;
  private credentialsParameterPath: string;
  private oauthEndpoint: string;

  constructor() {
    this.ssmClient = new SSMClient({
      region: process.env.REGION,
    });

    // OAuth credentials parameter path
    this.credentialsParameterPath = process.env.OAUTH_CREDENTIALS_PARAMETER;

    // OAuth endpoint
    this.oauthEndpoint = process.env.OAUTH_TOKEN_ENDPOINT;
  }

  /**
   * Get OAuth credentials from Parameter Store
   * @returns Promise<string> - Base64 encoded Basic auth credentials
   */
  async getSecret(): Promise<string> {
    try {
      const command = new GetParameterCommand({
        Name: this.credentialsParameterPath,
        WithDecryption: true, // Decrypt SecureString parameters (also works with String type)
      });

      const response = await this.ssmClient.send(command);

      if (!response.Parameter?.Value) {
        throw new Error(
          `Parameter ${this.credentialsParameterPath} not found or has no value`,
        );
      }

      return response.Parameter.Value;
    } catch (error) {
      console.error(
        `Error retrieving OAuth credentials from Parameter Store:`,
        error,
      );
      throw new Error(`Failed to retrieve OAuth credentials: ${error.message}`);
    }
  }

  /**
   * Generates OAuth access token using client credentials flow
   * @returns Promise<OAuthTokenResponse> - OAuth token response
   */
  async generateAccessToken(): Promise<OAuthTokenResponse> {
    try {
      // Get credentials from Parameter Store
      const basicAuthCredentials = await this.getSecret();

      // Make OAuth token request
      const response = await axios.post(this.oauthEndpoint, null, {
        headers: {
          Authorization: `Basic ${basicAuthCredentials}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        timeout: 10000, // 10 second timeout
      });

      if (!response.data.access_token) {
        throw new Error(`OAuth response does not contain access_token`);
      }

      console.log(`OAuth token generated successfully`);
      return response.data;
    } catch (error) {
      console.error(`Error generating OAuth access token:`, error);

      if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError;
        if (axiosError.response) {
          console.error('OAuth endpoint response:', {
            status: axiosError.response.status,
            data: axiosError.response.data,
          });
          throw new Error(
            `OAuth token generation failed: ${
              axiosError.response.status
            } - ${JSON.stringify(axiosError.response.data)}`,
          );
        } else if (axiosError.request) {
          throw new Error(
            `OAuth token generation failed: No response from OAuth endpoint`,
          );
        }
      }

      throw new Error(`OAuth token generation failed: ${error.message}`);
    }
  }

  /**
   * Validates that the OAuth credentials are properly configured
   * @returns Promise<boolean> - True if credentials are accessible
   */
  async validateCredentialsAccess(): Promise<boolean> {
    try {
      await this.getSecret();
      return true;
    } catch (error) {
      console.error(`OAuth credentials validation failed:`, error);
      return false;
    }
  }

  /**
   * Gets the credentials parameter path
   * @returns string - Credentials parameter path
   */
  getCredentialsParameterPath(): string {
    return this.credentialsParameterPath;
  }

  /**
   * Gets the OAuth endpoint
   * @returns string - OAuth endpoint URL
   */
  getOAuthEndpoint(): string {
    return this.oauthEndpoint;
  }
}
