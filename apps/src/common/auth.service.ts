import { Injectable } from '@nestjs/common';
import { CognitoJwtVerifier } from 'aws-jwt-verify';
import {
  CognitoIdentityProviderClient,
  ListUsersCommand,
  AdminDeleteUserCommand,
} from '@aws-sdk/client-cognito-identity-provider';
@Injectable()
export class AuthService {
  private client: CognitoIdentityProviderClient;

  constructor() {
    this.client = new CognitoIdentityProviderClient({
      region: process.env.REGION,
    });
  }
  async extractUserDetailsFromToken(token: string): Promise<any> {
    try {
      const verifier = CognitoJwtVerifier.create({
        userPoolId: process.env.APPHERO_USERPOOL_ID,
        tokenUse: 'id',
        clientId: process.env.APPHERO_USERPOOL_CLIENT_ID,
      });
      const payload = await verifier.verify(token);
      return payload;
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }
  async checkEmailExistInCognito(email): Promise<any> {
    try {
      const response = await this.getUsersByEmail(email);
      if (
        response.Users &&
        response.Users.length > 0 &&
        (response.Users[0].UserStatus === 'CONFIRMED' ||
          response.Users[0].UserStatus === 'EXTERNAL_PROVIDER')
      ) {
        console.log(response.Users[0]);
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error('Error getting user details:', error);
      throw error;
    }
  }
  async getUsersByEmail(email: string): Promise<any> {
    const input = {
      UserPoolId: process.env.APPHERO_USERPOOL_ID,
      Filter: `email = "${email}"`,
    };
    const command = new ListUsersCommand(input);
    return await this.client.send(command);
  }
  async deleteUserFromCognito(userDetails: any): Promise<void> {
    const deleteUserCommand = new AdminDeleteUserCommand({
      UserPoolId: process.env.APPHERO_USERPOOL_ID,
      Username: userDetails.Users[0].Username,
    });
    await this.client.send(deleteUserCommand);
    console.log(`Deleted unconfirmed user`);
  }
}
