import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { DynamoDBService } from '../common/dynamodb.service';
import { OpportunityService } from '../opportunity/service';
import { v4 as uuidv4 } from 'uuid';
import { LookUpService } from '../lookup/service';
import { LoggerService } from '../common/cloudwatchService';
import { LoggerEnum } from '@gus-eip/loggers';
const loggerEnum = new LoggerEnum();
@Injectable()
export class CmsContentService {
  constructor(
    private readonly dynamoDBService: DynamoDBService,
    private readonly opportunityService: OpportunityService,
    private readonly lookUpService: LookUpService,
    private readonly loggerService: LoggerService,
  ) {}

  async cmsContents(input: { email: string }, request: any) {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];

    try {
      const opportunities =
        await this.opportunityService.getOpportunitiesByEmail({
          email: input.email,
        });

      if (!opportunities?.response?.length) {
        throw new Error(`No opportunities found for email: ${input.email}`);
      }

      console.log('Opportunities fetched:', opportunities);

      const paramsBase = {
        TableName: process.env.APPHERO_SF_CMS_CONTENT_TABLE,
        KeyConditionExpression: 'PK = :pkValue',
        FilterExpression: 'Is_Active__c = :isActive',
        ExpressionAttributeValues: {
          ':isActive': '1',
        },
      };

      const pkExactMatch = new Set<string>();
      const pkValues = new Set<string>(['ALL#ALL#ALL']);
      const opportunitiesData = opportunities.response;

      const lookupPromises = opportunitiesData.map((opportunity) => {
        if (!opportunity?.BusinessUnitFilter__c || !opportunity.location) {
          return [];
        }
        return Promise.all([
          this.lookUpService.getLookUp(
            'Brand',
            opportunity.BusinessUnitFilter__c,
          ),
          this.lookUpService.getLookUp('Country', opportunity.location),
        ]).then(([brandResponse, countryResponse]) => {
          const institution = brandResponse?.Item?.cmsBrandCode || 'ALL';
          const countryCode = countryResponse?.Item?.Code || 'ALL';
          const applicationStatus =
            stageMapping[opportunity.StageName] || 'ALL';

          console.log('dnnd', countryCode, institution, applicationStatus);

          pkExactMatch.add(
            `CMSFeed#${countryCode}#${institution}#${applicationStatus}`,
          );
          [
            `CMSFeed#${countryCode}#${institution}#ALL`,
            `CMSFeed#${countryCode}#ALL#${applicationStatus}`,
            `CMSFeed#ALL#${institution}#${applicationStatus}`,
            `CMSFeed#ALL#ALL#${applicationStatus}`,
            `CMSFeed#ALL#${institution}#ALL`,
            `CMSFeed#${countryCode}#ALL#ALL`,
          ].forEach((pk) => pkValues.add(pk));
        });
      });

      // Wait for all the lookup calls to complete
      await Promise.all(lookupPromises);

      console.log('Exact PK Values ->', Array.from(pkExactMatch));
      console.log('PK Values ->', Array.from(pkValues));

      this.log(
        loggerEnum.Event.INITIATED_FETCH_CMS_CONTENTS,
        input,
        input,
        'Filter cms content possible partitions',
        requestId,
        '',
        'AccountEmail__c',
        input.email,
        pkValues,
        usecase,
      );

      const queryPromises = Array.from(pkValues).map((pkValue) => {
        const params = {
          ...paramsBase,
          ExpressionAttributeValues: {
            ...paramsBase.ExpressionAttributeValues,
            ':pkValue': pkValue,
          },
        };
        return this.dynamoDBService.queryObjects(params);
      });
      const queryPromisesExactMatchFeeds = Array.from(pkExactMatch).map(
        (pkValue) => {
          const params = {
            ...paramsBase,
            ExpressionAttributeValues: {
              ...paramsBase.ExpressionAttributeValues,
              ':pkValue': pkValue,
            },
          };
          return this.dynamoDBService.queryObjects(params);
        },
      );

      const [queryResponses, queryResponsesExactMatchFeeds] = await Promise.all(
        [
          Promise.all(queryPromises.filter(Boolean)),
          Promise.all(queryPromisesExactMatchFeeds.filter(Boolean)),
        ],
      );

      const feeds = queryResponses.flatMap((response) => response.Items);
      const exactMatchfeeds = queryResponsesExactMatchFeeds.flatMap(
        (response) => response.Items,
      );

      const prioritizedContent = exactMatchfeeds.sort((feed1, feed2) => {
        const pkParts1 = feed1.PK.split('#');
        const stage1 = pkParts1[3] === 'ALL' ? 0 : pkParts1[3];
        const pkParts2 = feed2.PK.split('#');
        const stage2 = pkParts2[3] === 'ALL' ? 0 : pkParts2[3];
        if (stage1 === stage2) {
          const recencyA = new Date(feed1.Published_Date__c).getTime();
          const recencyB = new Date(feed2.Published_Date__c).getTime();
          return recencyB - recencyA;
        }
        return stage2 - stage1;
      });

      const finalResult = [...prioritizedContent, ...feeds];

      this.log(
        loggerEnum.Event.COMPLETED_FETCH_CMS_CONTENTS,
        input,
        input,
        'Filter cms content completed',
        requestId,
        '',
        'AccountEmail__c',
        input.email,
        finalResult,
        usecase,
      );

      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        input,
        input,
        'Operation completed',
        requestId,
        '',
        'AccountEmail__c',
        input.email,
        finalResult,
        usecase,
      );

      return {
        total: finalResult.length,
        results: finalResult,
      };
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_FETCH_CMS_CONTENTS,
        input,
        input,
        error.message ? error.message : JSON.stringify(error),
        requestId,
        '',
        'AccountEmail__c',
        input.email,
        null,
        usecase,
      );

      throw error;
    }
  }

  async filterWebinarEvents(
    input: {
      email: string;
      isTaskRequired?: boolean;
    },
    request,
  ) {
    const requestId = request?.body?.requestId || uuidv4();
    try {
      this.log(
        loggerEnum.Event.INITIATED_FILTER_WEBINAR_EVENTS,
        input,
        input,
        'Filter webinar events initiated',
        requestId,
        input.email,
        'AccountEmail__c',
        input.email,
      );
      const opportunities =
        await this.opportunityService.getOpportunitiesByEmail(input);

      const paramsBase = {
        TableName: process.env.APPHERO_SF_CMS_CONTENT_TABLE,
        KeyConditionExpression: 'PK = :pkValue',
        FilterExpression:
          '(Is_Active__c = :isActive OR Is_Active__c = :isActive1) AND Event_Date_Time__c > :currentDateTime',
        ExpressionAttributeValues: {
          ':isActive': '1',
          ':isActive1': 'yes',
          ':currentDateTime': new Date().toISOString(),
        },
      };
      const processedPkValues = new Set<string>();

      const eventPromises =
        opportunities.response?.map(async (opportunity) => {
          if (!opportunity?.BusinessUnitFilter__c || !opportunity.location) {
            return [];
          }

          // Fetch brandCode and countryCode in parallel
          const [brandResponse, countryResponse] = await Promise.all([
            this.lookUpService.getLookUp(
              'Brand',
              opportunity.BusinessUnitFilter__c,
            ),
            this.lookUpService.getLookUp('Country', opportunity.location),
          ]);

          const brandCode = brandResponse?.Item?.cmsBrandCode || '';
          const countryCode = countryResponse?.Item?.Code || '';

          // Get multiple PK values as an array
          let pkValues: string[] = [];

          if (brandCode && countryCode) {
            pkValues = generateEventsPK({ brandCode, countryCode });
          } else if (countryCode) {
            pkValues = generateEventsPK({ countryCode });
          }

          console.log('Processing PKs:', pkValues);

          // Filter out already processed PKs
          const uniquePkValues = pkValues.filter(
            (pk) => !processedPkValues.has(pk),
          );
          uniquePkValues.forEach((pk) => processedPkValues.add(pk));

          if (uniquePkValues.length === 0) {
            return [];
          }

          // Fetch events for all PK values
          const eventQueries = uniquePkValues.map(async (pkValue) => {
            const params = {
              ...paramsBase,
              ExpressionAttributeValues: {
                ...paramsBase.ExpressionAttributeValues,
                ':pkValue': pkValue,
              },
            };

            console.log('params--->', params);

            const eventsResponse = await this.dynamoDBService.queryObjects(
              params,
            );
            return eventsResponse.Items || [];
          });

          return (await Promise.all(eventQueries)).flat();
        }) || [];

      // Wait for all event promises to resolve and flatten results
      const events = (await Promise.all(eventPromises)).flat().sort((a, b) => {
        return (
          (new Date(a?.Event_Date_Time__c) as any) -
          (new Date(b?.Event_Date_Time__c) as any)
        );
      });

      console.log('Final events:', events);
      this.log(
        loggerEnum.Event.COMPLETED_FILTER_WEBINAR_EVENTS,
        input,
        input,
        'Filter webinar events completed',
        requestId,
        input.email,
        'AccountEmail__c',
        input.email,
        { items: events, total: events.length },
      );
      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        input,
        input,
        'Operation completed',
        requestId,
        input.email,
        'AccountEmail__c',
        input.email,
        { items: events, total: events.length },
      );

      return { items: events, total: events.length };
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_FILTER_WEBINAR_EVENTS,
        input,
        input,
        error.message ? error.message : JSON.stringify(error),
        requestId,
        input.email,
        'AccountEmail__c',
        input.email,
      );
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  async log(
    apiEvent,
    sourcePayload,
    destinationPayload,
    logMessage,
    requestId?,
    email?,
    entityKey?,
    entityKeyField?,
    response?,
    usecase?,
  ) {
    await this.loggerService.log(
      requestId || uuidv4(),
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      loggerEnum.UseCase[`${usecase}`],
      sourcePayload,
      destinationPayload,
      logMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }

  async error(
    apiEvent,
    sourcePayload,
    destinationPayload,
    errorMessage,
    requestId?,
    email?,
    entityKey?,
    entityKeyField?,
    response?,
    usecase?,
  ) {
    await this.loggerService.error(
      requestId || uuidv4(),
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      loggerEnum.UseCase[`${usecase}`],
      sourcePayload,
      destinationPayload,
      errorMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }
}

const generateEventsPK = (eventFilter) => {
  const pkValues = [];

  const countryCode = eventFilter.countryCode;
  const brandCode = eventFilter.brandCode;

  if (countryCode && brandCode) {
    pkValues.push(`CMSEvent#${countryCode}#${brandCode}`);
    pkValues.push(`CMSEvent#${countryCode}#All`);
    pkValues.push(`CMSEvent#All#${brandCode}`);
  } else if (countryCode) {
    pkValues.push(`CMSEvent#${countryCode}`);
    pkValues.push(`CMSEvent#All`);
    pkValues.push(`CMSEvent#${countryCode}#All`);
  }
  pkValues.push(`CMSEvent#All#All`);
  pkValues.push(`CMSEvent#${countryCode}#All`);
  pkValues.push(`CMSEvent#All#${brandCode}`);
  pkValues.push(`CMSEvent#ALL#ALL`);
  pkValues.push(`CMSEvent#${countryCode}#ALL`);
  pkValues.push(`CMSEvent#ALL#${brandCode}`);
  return pkValues;
};

const stageMapping = {
  'Documents Stage': '2',
  'Admissions Stage': '3',
  Offer: '4',
  Payment: '5',
  Acceptance: '6',
  Visa: '7',
  'Closed Won': '8',
  'Closed Lost': '9',
  All: '0',
};

const stagePriority = [
  'Closed Won',
  'Visa',
  'Acceptance',
  'Payment',
  'Offer',
  'Admissions Stage',
  'Documents Stage',
  'Closed Lost',
  'All',
];
