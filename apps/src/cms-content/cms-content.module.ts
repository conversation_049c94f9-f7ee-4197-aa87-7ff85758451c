import { Module } from '@nestjs/common';
import { CmsContentController } from './cms-content.controller';
import { CmsContentService } from './cms-content.service';
import { CommonModule } from '../common/module';
import { OpportunityModule } from '../opportunity/module';
import { LookUpModule } from '../lookup/module';
import { LoggerModule } from '@gus-eip/loggers';

@Module({
  imports: [
    CommonModule,
    OpportunityModule,
    LookUpModule,
  ],
  controllers: [CmsContentController],
  providers: [CmsContentService],
})
export class CmsContentModule { }
