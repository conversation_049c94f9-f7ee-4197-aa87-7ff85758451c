import { Injectable, NotFoundException } from '@nestjs/common';
import { DynamoDBService } from '../common/dynamodb.service';
import { S3Service } from '../common/s3.service';
import { AccountService } from '../account/service';
import { AuthService } from '../common/auth.service';
import { OptimizedProfileService } from './optimized-profile.service';
import { v4 as uuidv4 } from 'uuid';
import { LoggerService } from '../common/cloudwatchService';
import { LoggerEnum } from '@gus-eip/loggers';
const loggerEnum = new LoggerEnum();
@Injectable()
export class ProfileService {
  constructor(
    private readonly dynamoDBService: DynamoDBService,
    private readonly s3Service: S3Service,
    private readonly accountService: AccountService,
    private readonly authService: AuthService,
    private readonly loggerService: LoggerService,
    private readonly optimizedProfileService: OptimizedProfileService,
  ) {}
  async createProfile(profileDetails, request?): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];
    try {
      const profileTableDetails = {
        Item: {
          PK: profileDetails?.email.toLowerCase(),
          ...profileDetails,
          email: profileDetails?.email.toLowerCase(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      };
      this.log(
        loggerEnum.Event.INITIATED_CREATE_STUDENT_PROFILE,
        profileDetails,
        profileTableDetails,
        'Create profile initiated',
        requestId,
        profileDetails?.email,
        'userEmail',
        profileDetails?.email,
        null,
        usecase,
      );
      console.log(process.env.APPHERO_USER_DETAILS_TABLE);
      await this.dynamoDBService.putObject(
        process.env.APPHERO_USER_DETAILS_TABLE,
        profileTableDetails,
      );

      this.log(
        loggerEnum.Event.COMPLETED_CREATE_STUDENT_PROFILE,
        profileDetails,
        profileTableDetails,
        'Create profile completed',
        requestId,
        profileDetails?.email,
        'userEmail',
        profileDetails?.email,
        null,
        usecase,
      );
      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        profileDetails,
        {},
        'Operation completed successfully',
        requestId,
        profileDetails?.email,
        'userEmail',
        profileDetails?.email,
        null,
        usecase,
      );
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_CREATE_STUDENT_PROFILE,
        profileDetails,
        profileDetails,
        error.message ? error.message : JSON.stringify(error),
        requestId,
        profileDetails?.email,
        'userEmail',
        profileDetails?.email,
        null,
        usecase,
      );
      throw error;
    }
  }
  async updateProfile(profileDetails, email, request?): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];
    try {
      if (profileDetails?.base64ProfilePicture) {
        const buffer = Buffer.from(
          profileDetails?.base64ProfilePicture,
          'base64',
        );
        const bucketName = process.env.PROFILE_PICTURE_BUCKET;
        const objectKey = `${email}/${profileDetails?.fileName}`;
        const params = {
          Bucket: bucketName,
          Key: objectKey,
          Body: buffer,
        };
        this.log(
          loggerEnum.Event.INITIATED_UPDATE_STUDENT_PROFILE,
          profileDetails,
          profileDetails,
          'Update profile initiated',
          requestId,
          email,
          'userEmail',
          email,
          null,
          usecase,
        );
        let result;
        try {
          result = await this.s3Service.uploadObject(params);
          if (profileDetails.hasOwnProperty('base64ProfilePicture')) {
            delete profileDetails['base64ProfilePicture'];
          }
        } catch (error) {
          throw new Error(`Failed to upload file: ${error.message}`);
        }
        profileDetails['profilePictureLocation'] = result?.Location;
      }
      const PK = email;
      await this.dynamoDBService.updateObject(
        process.env.APPHERO_USER_DETAILS_TABLE,
        { PK },
        {
          ...profileDetails,
          updatedAt: new Date().toISOString(),
        },
      );

      this.log(
        loggerEnum.Event.COMPLETED_UPDATE_STUDENT_PROFILE,
        profileDetails,
        profileDetails,
        'Update profile completed',
        requestId,
        email,
        'userEmail',
        email,
        null,
        usecase,
      );
      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        profileDetails,
        profileDetails,
        'Operation completed',
        requestId,
        email,
        'userEmail',
        email,
        null,
        usecase,
      );
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_UPDATE_STUDENT_PROFILE,
        profileDetails,
        profileDetails,
        error.message ? error.message : JSON.stringify(error),
        requestId,
        email,
        'userEmail',
        email,
        null,
        usecase,
      );
      throw error;
    }
  }
  async getProfile(email, request?): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];
    let profileDetails;
    this.log(
      loggerEnum.Event.INITIATED_GET_STUDENT_PROFILE,
      { email },
      { email },
      'Get profile initiated',
      requestId,
      email,
      'userEmail',
      email,
      null,
      usecase,
    );
    try {
      profileDetails = await this.dynamoDBService.getObject(
        process.env.APPHERO_USER_DETAILS_TABLE,
        {
          PK: email,
        },
      );
      let accounts;
      const accountInfo =
        await this.accountService.getPersonAccountDetailsFromDb(email);
      accounts = accountInfo.Items;
      if (accounts.length === 0) {
        // Use optimized direct Salesforce call instead of middleware
        accounts = await this.optimizedProfileService.getPersonAccountOptimized(
          email,
        );
        await this.optimizedProfileService.savePersonAccountDetailsToDB(
          accounts,
        );
      }

      profileDetails = await {
        ...profileDetails?.Item,
        canApply: this.isAnyAppHeroCanApplyTrue(accounts),
      };
      this.log(
        loggerEnum.Event.COMPLETED_GET_STUDENT_PROFILE,
        { email },
        { email },
        'Get profile completed',
        requestId,
        email,
        'userEmail',
        email,
        profileDetails,
        usecase,
      );
      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        { email },
        { email },
        'Operation completed',
        requestId,
        email,
        'userEmail',
        email,
        profileDetails,
        usecase,
      );
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_GET_STUDENT_PROFILE,
        { email },
        { email },
        error.message ? error.message : JSON.stringify(error),
        requestId,
        email,
        'userEmail',
        email,
        null,
        usecase,
      );
      throw error;
    }
    return profileDetails;
  }
  isAnyAppHeroCanApplyTrue(records) {
    console.log(records);
    for (const record of records) {
      if (record.App_Hero_Can_Apply__c === true) {
        return true;
      }
    }
    return false;
  }
  async isAlreadySignedUp(payload, request?): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();
    const email = payload?.email;

    await this.log(
      'INITIATED_GET_USER_FROM_COGNITO',
      payload,
      { email },
      'Get user from Cognito initiated',
      requestId,
      email,
      'userEmail',
      email,
    );
    const userDetails = await this.authService.getUsersByEmail(email);
    await this.log(
      'COMPLETED_GET_USER_FROM_COGNITO',
      payload,
      { email },
      'Get user from Cognito completed',
      requestId,
      email,
      'userEmail',
      email,
      JSON.stringify(userDetails),
    );

    if (!userDetails.Users.length) {
      await this.log(
        'USER_NOT_EXIST_IN_COGNITO',
        { email },
        { email },
        'User does not exist in Cognito',
        requestId,
        email,
        'userEmail',
        email,
      );
      return { exists: false };
    }

    const users = userDetails.Users;
    const hasFederatedAccount = users.some(
      (user) => user.UserStatus === 'EXTERNAL_PROVIDER',
    );
    const hasConfirmedAccount = users.some(
      (user) => user.UserStatus === 'CONFIRMED',
    );
    const hasUnconfirmedAccount = users.some(
      (user) => user.UserStatus === 'UNCONFIRMED',
    );

    if (payload.source === 'PreSignUp_SignUp') {
      if (hasFederatedAccount) {
        await this.log(
          'FEDERATED_LOGIN_EXIST_IN_COGNITO',
          { email },
          { email },
          'Federated login exists in Cognito',
          requestId,
          email,
          'userEmail',
          email,
        );
        return { exists: true };
      }

      if (hasUnconfirmedAccount) {
        await this.log(
          'UNCONFIRMED_LOGIN_EXIST_IN_COGNITO',
          { email },
          { email },
          'Unconfirmed login exists in Cognito',
          requestId,
          email,
          'userEmail',
          email,
        );
        await this.deleteUser(userDetails, email, requestId);
        return { exists: false };
      }

      if (hasConfirmedAccount) {
        return { exists: true, reason: 'CONFIRMED_USER' };
      }
    } else if (payload.source === 'PreSignUp_ExternalProvider') {
      if (hasConfirmedAccount) {
        await this.log(
          'LOGIN_EXIST_IN_COGNITO',
          { email },
          { email },
          'Login exists in Cognito',
          requestId,
          email,
          'userEmail',
          email,
        );
        return { exists: true };
      }

      if (hasUnconfirmedAccount) {
        await this.deleteUser(userDetails, email, requestId);
        return { exists: false };
      }

      if (hasFederatedAccount) {
        return { exists: false };
      }
    }

    return { exists: false };
  }

  async deleteUser(userDetails, email, requestId): Promise<void> {
    console.log(`Deleting unconfirmed user: ${email}`);
    this.log(
      loggerEnum.Event.INITIATED_DELETE_USER_FROM_COGNITO,
      { email },
      { email },
      'Delete user from cognito initiated',
      requestId,
      email,
      'userEmail',
      email,
    );
    await this.authService.deleteUserFromCognito(userDetails);
    this.log(
      loggerEnum.Event.COMPLETED_DELETE_USER_FROM_COGNITO,
      { email },
      { email },
      'Delete User from cognito completed',
      requestId,
      email,
      'userEmail',
      email,
    );
    this.log(
      loggerEnum.Event.OPERATION_COMPLETED,
      { email },
      { email },
      'Operation completed',
      requestId,
      email,
      'userEmail',
      email,
    );
  }
  async deleteUserFromCognito(email: string, request?): Promise<void> {
    const requestId = request?.body?.requestId || uuidv4();
    try {
      this.log(
        loggerEnum.Event.INITIATED_DELETE_USER_FROM_COGNITO,
        { email },
        { email },
        'Delete user from cognito initiated',
        requestId,
        email,
        'userEmail',
        email,
      );
      const userDetails = await this.authService.getUsersByEmail(email);
      if (userDetails.Users.length === 0) {
        this.log(
          loggerEnum.Event.NOT_FOUND_USER,
          { email },
          { email },
          'User not found',
          requestId,
          email,
          'userEmail',
          email,
        );
        console.log('User not found');
        return;
      } else if (
        userDetails.Users.length > 0 &&
        userDetails.Users[0].UserStatus === 'UNCONFIRMED'
      ) {
        console.log(`Deleting unconfirmed user: ${email}`);
        await this.authService.deleteUserFromCognito(userDetails);
        this.log(
          loggerEnum.Event.COMPLETED_DELETE_USER_FROM_COGNITO,
          { email },
          { email },
          'Delete User from cognito completed',
          requestId,
          email,
          'userEmail',
          email,
        );
        this.log(
          loggerEnum.Event.OPERATION_COMPLETED,
          { email },
          { email },
          'Operation completed',
          requestId,
          email,
          'userEmail',
          email,
        );
      }
      return;
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_DELETE_USER_FROM_COGNITO,
        { email },
        { email },
        error.message ? error.message : JSON.stringify(error),
        requestId,
        email,
        'userEmail',
        email,
      );
    }
  }

  async log(
    apiEvent,
    sourcePayload,
    destinationPayload,
    logMessage,
    requestId?,
    email?,
    entityKey?,
    entityKeyField?,
    response?,
    usecase?,
  ) {
    await this.loggerService.log(
      requestId || uuidv4(),
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      loggerEnum.UseCase[`${usecase}`],
      sourcePayload,
      destinationPayload,
      logMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }

  async error(
    apiEvent,
    sourcePayload,
    destinationPayload,
    errorMessage,
    requestId?,
    email?,
    entityKey?,
    entityKeyField?,
    response?,
    usecase?,
  ) {
    await this.loggerService.error(
      requestId || uuidv4(),
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      usecase,
      sourcePayload,
      destinationPayload,
      errorMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }
}
