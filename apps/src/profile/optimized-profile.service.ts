import { Injectable } from '@nestjs/common';
import { DirectSalesforceService } from '../common/direct-salesforce.service';
import { DynamoDBService } from '../common/dynamodb.service';
import { S3Service } from '../common/s3.service';
import { AuthService } from '../common/auth.service';
import { LoggerService } from '../common/cloudwatchService';
import { LoggerEnum } from '@gus-eip/loggers';
import { v4 as uuidv4 } from 'uuid';

const loggerEnum = new LoggerEnum();

@Injectable()
export class OptimizedProfileService {
  constructor(
    private readonly directSalesforceService: DirectSalesforceService,
    private readonly dynamoDBService: DynamoDBService,
    private readonly s3Service: S3Service,
    private readonly authService: AuthService,
    private readonly loggerService: LoggerService,
  ) {}

  /**
   * Optimized method to get person account using direct Salesforce calls
   * instead of middleware service calls
   */
  async getPersonAccountOptimized(email: string, request?): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();

    this.log(
      loggerEnum.Event.INITIATED_GET_PERSON_ACCOUNT,
      { email },
      { email },
      'Get person account initiated (optimized)',
      requestId,
      email,
      'Email__c',
      email,
    );

    try {
      // Use direct Salesforce API call instead of middleware
      const response =
        await this.directSalesforceService.getPersonAccountByEmail(email);
      const accountDetails = response.records || [];

      this.log(
        loggerEnum.Event.COMPLETED_GET_PERSON_ACCOUNT,
        { email },
        { email },
        'Get person account completed (optimized)',
        requestId,
        email,
        'Email__c',
        email,
        accountDetails,
      );

      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        { email },
        { email },
        'Operation completed (optimized)',
        requestId,
        email,
        'Email__c',
        email,
        accountDetails,
      );

      return accountDetails;
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_GET_PERSON_ACCOUNT,
        { email },
        { email },
        error.message ? error.message : JSON.stringify(error),
        requestId,
        email,
        'Account',
        email,
      );
      throw error;
    }
  }

  /**
   * Optimized method to save person account details to DB
   */
  async savePersonAccountDetailsToDB(details: any[]): Promise<void> {
    const promises = details.map(async (personAccountsDetails) => {
      await this.dynamoDBService.putObject(
        process.env.APPHERO_SF_ACCOUNT_TABLE,
        {
          Item: {
            PK: personAccountsDetails?.PersonEmail,
            SK: `${personAccountsDetails?.RecordTypeId}_${personAccountsDetails?.Id}`,
            ...personAccountsDetails,
            createdAt: new Date().toISOString(),
          },
        },
      );
    });

    await Promise.all(promises);
  }

  /**
   * Optimized method to get person account details from DB
   */
  async getPersonAccountDetailsFromDB(email: string): Promise<any> {
    const queryParams = {
      TableName: process.env.APPHERO_SF_ACCOUNT_TABLE,
      KeyConditionExpression: 'PK = :pk and begins_with(SK, :skValue)',
      ExpressionAttributeValues: {
        ':pk': email,
        ':skValue': `${process.env.PERSON_ACCOUNT_RECORDTYPE_ID}`,
      },
    };
    return await this.dynamoDBService.queryObjects(queryParams);
  }

  /**
   * Check if any AppHero can apply is true
   */
  isAnyAppHeroCanApplyTrue(accounts: any[]): boolean {
    return accounts.some((account) => account.AppHero_Can_Apply__c === true);
  }

  /**
   * Optimized profile retrieval that uses direct Salesforce calls
   */
  async getProfileOptimized(email: string, request?): Promise<any> {
    const requestId = request?.body?.requestId || uuidv4();
    const usecase = request?.headers?.['x-screen-name'];

    this.log(
      loggerEnum.Event.INITIATED_GET_STUDENT_PROFILE,
      { email },
      { email },
      'Get profile initiated (optimized)',
      requestId,
      email,
      'userEmail',
      email,
      null,
      usecase,
    );

    try {
      // Get profile details from DynamoDB
      let profileDetails: any = await this.dynamoDBService.getObject(
        process.env.APPHERO_USER_DETAILS_TABLE,
        {
          PK: email,
        },
      );

      // Get account info using optimized method
      let accounts;
      const accountInfo = await this.getPersonAccountDetailsFromDB(email);
      accounts = accountInfo.Items;

      if (accounts.length === 0) {
        // Use optimized direct Salesforce call
        accounts = await this.getPersonAccountOptimized(email);
        await this.savePersonAccountDetailsToDB(accounts);
      }

      profileDetails = {
        ...profileDetails?.Item,
        canApply: this.isAnyAppHeroCanApplyTrue(accounts),
      };

      this.log(
        loggerEnum.Event.COMPLETED_GET_STUDENT_PROFILE,
        { email },
        { email },
        'Get profile completed (optimized)',
        requestId,
        email,
        'userEmail',
        email,
        profileDetails,
        usecase,
      );

      return profileDetails;
    } catch (error) {
      this.error(
        loggerEnum.Event.FAILED_GET_STUDENT_PROFILE,
        { email },
        { email },
        error.message ? error.message : JSON.stringify(error),
        requestId,
        email,
        'userEmail',
        email,
        null,
        usecase,
      );
      throw error;
    }
  }

  /**
   * Log method for tracking optimized operations
   */
  async log(
    apiEvent: string,
    sourcePayload: any,
    destinationPayload: any,
    logMessage: string,
    requestId?: string,
    email?: string,
    entityKey?: string,
    entityKeyField?: string,
    response?: any,
    usecase?: string,
  ) {
    await this.loggerService.log(
      requestId || uuidv4(),
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      '',
      sourcePayload,
      destinationPayload,
      logMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }

  /**
   * Error logging method for optimized operations
   */
  async error(
    apiEvent: string,
    sourcePayload: any,
    destinationPayload: any,
    errorMessage: string,
    requestId?: string,
    email?: string,
    entityKey?: string,
    entityKeyField?: string,
    response?: any,
    usecase?: string,
  ) {
    await this.loggerService.error(
      requestId || uuidv4(),
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.GUS_EIP_SERVICE,
      apiEvent,
      '',
      sourcePayload,
      destinationPayload,
      errorMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }
}
