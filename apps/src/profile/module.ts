import { Module } from '@nestjs/common';
import { ProfileController } from './controller';
import { ProfileService } from './service';
import { OptimizedProfileService } from './optimized-profile.service';
import { CommonModule } from '../common/module';
import { AccountModule } from '../account/module';
import { LoggerModule } from '@gus-eip/loggers';
@Module({
  imports: [CommonModule, AccountModule],
  controllers: [ProfileController],
  providers: [ProfileController, ProfileService, OptimizedProfileService],
  exports: [ProfileController],
})
export class ProfileModule {}
