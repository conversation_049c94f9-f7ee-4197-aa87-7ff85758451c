import {
  Controller,
  Get,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Delete,
  Req,
} from '@nestjs/common';
import { ProfileService } from './service';
import { Request } from 'express';
import { request } from 'http';

@Controller('/')
export class ProfileController {
  constructor(private readonly profileService: ProfileService) {}
  @Post('apphero/profile')
  async createProfile(@Body() event, @Req() request: Request): Promise<any> {
    try {
      await this.profileService.createProfile(event, request);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Post('unauth/apphero/profile')
  async createUnauthProfile(
    @Body() event,
    @Req() request: Request,
  ): Promise<any> {
    try {
      await this.profileService.createProfile(event, request);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Patch('apphero/profile/:email')
  async updateProfile(
    @Body() event,
    @Param('email') email: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      await this.profileService.updateProfile(
        event,
        email.toLowerCase(),
        request,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('apphero/profile/:email')
  async getProfile(
    @Param('email') email: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      return await this.profileService.getProfile(email.toLowerCase(), request);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Delete('unauth/apphero/profile/:email')
  async deleteUnconfirmedUsersFromCognito(
    @Param('email') email: string,
    @Req() request: Request,
  ): Promise<any> {
    return await this.profileService.deleteUserFromCognito(
      email.toLowerCase(),
      request,
    );
  }
  @Post('unauth/apphero/userexist')
  async canSignUp(@Body() event, @Req() request: Request): Promise<any> {
    return await this.profileService.isAlreadySignedUp(event, request);
  }
}
