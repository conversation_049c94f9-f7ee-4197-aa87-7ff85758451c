import { LookUpModule } from '../lookup/module';
import { Module } from '@nestjs/common';
import { CobrandingController } from './controller';
import { CobrandingService } from './service';
import { CommonModule } from '../common/module';
import { LoggerModule } from '@gus-eip/loggers';
@Module({
  imports: [
    CommonModule,
    LookUpModule,
  ],
  controllers: [CobrandingController],
  providers: [CobrandingController, CobrandingService],
  exports: [CobrandingController, CobrandingService],
})
export class CobrandingModule { }
