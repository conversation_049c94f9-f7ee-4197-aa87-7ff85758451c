import {
  Controller,
  Get,
  Query,
  BadRequestException,
  HttpException,
  HttpStatus,
  Param,
  Req,
} from '@nestjs/common';
import { CobrandingService } from './service';
import { Request } from 'express';

@Controller('')
export class CobrandingController {
  constructor(private readonly cobrandingService: CobrandingService) {}

  @Get('unauth/apphero/cobrandedurl')
  async generateLink(
    @Query('email') email: string,
    @Query('brand') brand: string,
    @Req() request: Request,
  ) {
    if (!email || !brand || email === undefined || brand === undefined) {
      throw new BadRequestException(
        'Email and brand are mandatory query parameters',
      );
    }
    const appHeroUrl = await this.cobrandingService.generateLink(
      email.toLowerCase(),
      brand,
      request,
    );
    return {
      messageCode: 200,
      appHeroUrl,
    };
  }
  @Get('unauth/apphero/cobranddetail/:id')
  async getBrandConfigAndEmailStatusById(
    @Param('id') id: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      return await this.cobrandingService.getBrandConfigurationAndEmailStatusById(
        id,
        request,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
