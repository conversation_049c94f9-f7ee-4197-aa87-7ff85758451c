import { Modu<PERSON> } from '@nestjs/common';
import { ChatController } from './controller';
import { ChatService } from './service';
import { CommonModule } from '../common/module';
import { OpportunityModule } from '../opportunity/module';
import { LoggerModule } from '@gus-eip/loggers';
@Module({
    imports: [
        CommonModule,
        OpportunityModule,
    ],
    controllers: [ChatController],
    providers: [ChatController, ChatService],
    exports: [ChatController,],
})
export class ChatModule { }
