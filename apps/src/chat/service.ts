import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { SalesforceService } from '../common/salesforce.service';
import { LoggerService } from '../common/cloudwatchService';
import { LoggerEnum } from '@gus-eip/loggers';
import { SqsService } from '../common/sqs.service';
const loggerEnum = new LoggerEnum();
@Injectable()
export class ChatService {
  constructor(
    private sqsService: SqsService,
    private readonly loggerService: LoggerService,
  ) {}
  async queueNewCaseRecord(data: any, requestId: string) {
    try {
      this.log(
        loggerEnum.Event.PUBLISH_MESSAGE_TO_SQS,
        data,
        data,
        'Initiate Publish Message',
        requestId,
        data?.contactEmailId,
        'opportunityId',
        data?.opportunityId,
      );

      this.validateEmails([
        data.contactEmailId,
        ...(data.agent ? [data.agent] : []),
      ]);

      const response = await this.sqsService.sendMessage(
        JSON.stringify(data),
        process.env.APPHERO_CHATBOT_REQUEST_QUEUE_URL,
        process.env.REGION,
        data.conversationId,
      );

      this.log(
        loggerEnum.Event.OPERATION_COMPLETED,
        data,
        data,
        'Operation completed',
        requestId,
        data?.contactEmailId,
        'opportunityId',
        data?.opportunityId,
        response,
      );
      return response;
    } catch (error) {
      this.error(
        loggerEnum.Event.PUBLISH_MESSAGE_TO_SQS,
        data,
        data,
        error.message ? error.message : JSON.stringify(error),
        requestId,
        '',
        'opportunityId',
        data?.opportunityId,
      );
      throw error;
    }
  }

  private validateEmails(emails: string[]) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const invalidEmails = emails.filter((email) => !emailRegex.test(email));

    if (invalidEmails.length > 0) {
      const invalidEmailsMessage = invalidEmails.join(', ');
      const errorMessage =
        invalidEmails.length === 1
          ? `Invalid email address: ${invalidEmailsMessage}`
          : `Invalid email addresses: ${invalidEmailsMessage}`;
      throw new BadRequestException(errorMessage);
    }
  }

  async log(
    apiEvent,
    sourcePayload,
    destinationPayload,
    logMessage,
    requestId?,
    email?,
    entityKey?,
    entityKeyField?,
    response?,
  ) {
    await this.loggerService.log(
      requestId,
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.APPHERO_CHATBOT_CASE_REQUEST_QUEUE,
      apiEvent,
      '',
      sourcePayload,
      destinationPayload,
      logMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }

  async error(
    apiEvent,
    sourcePayload,
    destinationPayload,
    errorMessage,
    requestId?,
    email?,
    entityKey?,
    entityKeyField?,
    response?,
  ) {
    await this.loggerService.error(
      requestId,
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_BACKEND,
      loggerEnum.Component.APPHERO_FRONTEND,
      loggerEnum.Component.APPHERO_CHATBOT_CASE_REQUEST_QUEUE,
      apiEvent,
      '',
      sourcePayload,
      destinationPayload,
      errorMessage,
      '',
      email,
      entityKey,
      entityKeyField,
      '',
      '',
      '',
      '',
      response,
    );
  }
}
