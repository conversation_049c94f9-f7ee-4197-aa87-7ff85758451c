import {
  Body,
  Controller,
  ForbiddenException,
  HttpException,
  HttpStatus,
  Post,
  Req,
} from '@nestjs/common';
import { ChatService } from './service';
import { OpportunityService } from '../opportunity/service';
import { Request } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { LoggerEnum } from '@gus-eip/loggers';
const loggerEnum = new LoggerEnum();

@Controller()
export class ChatController {
  constructor(
    private readonly chatService: ChatService,
    private readonly opportunityService: OpportunityService,
  ) {}

  @Post('apphero/chat/case')
  async createCaseRecord(@Body() event, @Req() request: Request) {
    const requestId = request?.body?.requestId || uuidv4();
    try {
      const requiredFields = [
        'type',
        'agent',
        'description',
        'transcript',
        'subject',
        'contactEmailId',
        'opportunityId',
        'conversationId',
      ];
      const missingOrEmptyFields = [];
      for (let field of requiredFields) {
        if (
          field !== 'agent' &&
          (!event.hasOwnProperty(field) || !event[field])
        ) {
          missingOrEmptyFields.push(field);
        }
      }
      if (missingOrEmptyFields.length > 0) {
        throw new ForbiddenException(
          `The following required fields are either missing or empty: ${missingOrEmptyFields.join(
            ', ',
          )}.`,
        );
      }
      const response = await this.chatService.queueNewCaseRecord(
        event,
        requestId,
      );

      return response;
    } catch (error) {
      await this.chatService.error(
        loggerEnum.Event.CREATE_CASE_RECORD,
        event,
        event,
        error,
        requestId,
        event?.contactEmailId,
        'opportunityId',
        event?.opportunityId,
      );
      throw error;
    }
  }

  @Post('apphero/chat/opportunitiesbyemail')
  async OpportunitiesByEmail(@Body() event: any): Promise<any> {
    try {
      const opportunities =
        await this.opportunityService.getOpportunitiesByEmail(event);
      let opportunitiesList = [];
      opportunities.response.forEach((opportunity) => {
        if (opportunity.ApplicationStatus.status !== 'Draft') {
          opportunitiesList.push({
            OpportunityId: opportunity.Id,
            Name: [
              opportunity.ProgrammeName__c,
              opportunity.Institution_Full_Name__c,
              opportunity.Delivery_Mode__c,
              opportunity.location,
              opportunity.Product_Intake_Date__c
                ? new Date(opportunity.Product_Intake_Date__c)
                    .toLocaleDateString('en-US', {
                      month: 'short',
                      year: 'numeric',
                    })
                    .split(' ')
                    .join('-')
                : new Date(
                    opportunity.OpportunityLineItems?.records?.[0]?.Intake_Date__c,
                  )
                    .toLocaleDateString('en-US', {
                      month: 'short',
                      year: 'numeric',
                    })
                    .split(' ')
                    .join('-') ?? '',
            ]
              .filter((val) => val)
              .join(', '),
            Brand: opportunity.Brand__c,
            InstitutionName: opportunity.Institution,
          });
        }
      });
      return opportunitiesList;
    } catch (error) {
      console.log(error);
      throw new HttpException(
        error.response?.data || 'Unable to get Opportunities List',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
